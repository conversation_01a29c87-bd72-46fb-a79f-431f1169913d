# Database Setup Troubleshooting Guide

## Permission Denied Error

If you're getting `permission denied for schema public`, this is due to PostgreSQL 15+ security changes. Here are several solutions:

### Solution 1: Quick Fix (Recommended for Development)

Run this as the PostgreSQL superuser (usually `postgres`):

```bash
# Connect as postgres superuser
sudo -u postgres psql

# Or if you have postgres user password
psql -U postgres -h localhost
```

Then run:
```sql
-- Grant permissions to public schema
GRANT CREATE ON SCHEMA public TO PUBLIC;
GRANT USAGE ON SCHEMA public TO PUBLIC;

-- Set default privileges
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO PUBLIC;
```

### Solution 2: Create Dedicated User (Recommended for Production)

```bash
# Connect as postgres superuser
sudo -u postgres psql
```

```sql
-- Create application user
CREATE USER compliance_app WITH PASSWORD 'your_secure_password';

-- Create database with owner
CREATE DATABASE compliance_command_center OWNER compliance_app;

-- Grant permissions
GRANT CREATE ON SCHEMA public TO compliance_app;
GRANT USAGE ON SCHEMA public TO compliance_app;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO compliance_app;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO compliance_app;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO compliance_app;
```

Then use the new user:
```bash
export DB_USER=compliance_app
export PGPASSWORD=your_secure_password
./setup_database.sh
```

### Solution 3: Use Automated Permission Setup

```bash
# Run the permission setup script as postgres user
sudo -u postgres psql -f setup_permissions.sql

# Then run the main setup
./setup_database.sh
```

### Solution 4: Manual Step-by-Step Setup

1. **Create database as superuser:**
```bash
sudo -u postgres createdb compliance_command_center
```

2. **Grant permissions:**
```bash
sudo -u postgres psql compliance_command_center -c "
GRANT CREATE ON SCHEMA public TO $USER;
GRANT USAGE ON SCHEMA public TO $USER;
"
```

3. **Apply schema:**
```bash
psql compliance_command_center -f database_schema.sql
```

## Common Issues and Solutions

### Issue: "role does not exist"
**Solution:** Create the user first:
```sql
CREATE USER your_username;
```

### Issue: "database does not exist"
**Solution:** Create the database:
```bash
createdb compliance_command_center
```

### Issue: "connection refused"
**Solutions:**
1. Check if PostgreSQL is running: `sudo systemctl status postgresql`
2. Start PostgreSQL: `sudo systemctl start postgresql`
3. Check connection settings in `pg_hba.conf`

### Issue: "authentication failed"
**Solutions:**
1. Set password: `export PGPASSWORD=your_password`
2. Use peer authentication: `sudo -u postgres psql`
3. Check `pg_hba.conf` authentication methods

## Environment-Specific Instructions

### Ubuntu/Debian
```bash
# Install PostgreSQL
sudo apt update
sudo apt install postgresql postgresql-contrib

# Start service
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Setup
sudo -u postgres psql -f setup_permissions.sql
./setup_database.sh
```

### CentOS/RHEL/Fedora
```bash
# Install PostgreSQL
sudo dnf install postgresql postgresql-server postgresql-contrib

# Initialize database
sudo postgresql-setup --initdb

# Start service
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Setup
sudo -u postgres psql -f setup_permissions.sql
./setup_database.sh
```

### macOS (Homebrew)
```bash
# Install PostgreSQL
brew install postgresql

# Start service
brew services start postgresql

# Setup (you might be the superuser already)
psql postgres -f setup_permissions.sql
./setup_database.sh
```

### Docker
```bash
# Run PostgreSQL container
docker run --name postgres-compliance \
  -e POSTGRES_PASSWORD=mypassword \
  -e POSTGRES_DB=compliance_command_center \
  -p 5432:5432 \
  -d postgres:15

# Wait for container to start
sleep 10

# Setup
export PGPASSWORD=mypassword
export DB_USER=postgres
./setup_database.sh
```

## Verification Commands

After setup, verify everything works:

```bash
# Check connection
psql compliance_command_center -c "SELECT version();"

# Check tables
psql compliance_command_center -c "\dt"

# Check sample data
psql compliance_command_center -c "SELECT COUNT(*) FROM compliance_rules;"

# Check permissions
psql compliance_command_center -c "
SELECT has_schema_privilege(current_user, 'public', 'CREATE') as can_create,
       has_schema_privilege(current_user, 'public', 'USAGE') as can_use;
"
```

## Alternative: Use Minimal Schema

If you're still having issues, try the minimal schema first:

```bash
# Use minimal schema (no complex permissions needed)
psql compliance_command_center -f minimal_schema.sql
```

## Production Considerations

For production environments:

1. **Create dedicated database user**
2. **Use strong passwords**
3. **Limit permissions to only what's needed**
4. **Enable SSL connections**
5. **Configure proper authentication in pg_hba.conf**
6. **Set up regular backups**

Example production setup:
```sql
-- Create dedicated user with limited privileges
CREATE USER compliance_app WITH PASSWORD 'strong_random_password';
CREATE DATABASE compliance_command_center OWNER compliance_app;

-- Grant only necessary permissions
GRANT CONNECT ON DATABASE compliance_command_center TO compliance_app;
GRANT CREATE ON SCHEMA public TO compliance_app;
GRANT USAGE ON SCHEMA public TO compliance_app;
```

## Getting Help

If you're still having issues:

1. Check PostgreSQL logs: `sudo tail -f /var/log/postgresql/postgresql-*.log`
2. Verify PostgreSQL version: `psql --version`
3. Check current user permissions: `psql -c "\du"`
4. Review connection settings: `psql -c "SHOW hba_file;"`

## Contact

For additional help with the database setup, please check:
- PostgreSQL documentation: https://www.postgresql.org/docs/
- This project's README: `DATABASE_SCHEMA.md`
- The schema file: `database_schema.sql`
