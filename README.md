# Compliance Command Center DBOS

A comprehensive compliance management system built on the DBOS (Database-Oriented Operating System) framework. This application provides tools for regulatory compliance monitoring, Know Your Customer (KYC) processing, document analysis, and compliance reporting.

## 🚀 Features

- **Compliance Document Management**: Upload, scan, and analyze documents for regulatory compliance
- **KYC Processing**: Manage customer profiles with secure data handling and risk assessment
- **Regulatory Monitoring**: Track regulatory updates and compliance standards
- **Workflow Automation**: Automated compliance checks and KYC processing workflows
- **Compliance Reporting**: Generate comprehensive compliance reports
- **Dashboard Analytics**: Real-time insights into compliance status and performance metrics

## 📋 Prerequisites

- Node.js 18+ and npm/bun
- PostgreSQL 12+ with UUID and pgcrypto extensions
- Database user with CREATE privileges

## 🔧 Quick Setup

1. **Clone the repository**

```bash
git clone https://github.com/your-org/compliance-command-center-dbos.git
cd compliance-command-center-dbos
```

2. **Install dependencies**

```bash
npm install
# or
bun install
```

3. **Set up the database**

```bash
# Run the quick setup script
./quick_setup.sh

# Or manually:
createdb dbos_kyc_demo
psql dbos_kyc_demo -f database_schema.sql
```

4. **Configure environment**

```bash
export DBOS_DATABASE_URL="postgresql://dbosadmin@localhost:5432/dbos_kyc_demo"
```

5. **Start the application**

```bash
npm run dev
# or
bun run dev
```

## 🏗️ Project Structure

- `/src`: Source code for the application
  - `/src/index.ts`: Main application entry point with API endpoints and DBOS workflows
- `/public`: Static assets
- `/migrations`: Database migration files
- `/scripts`: Utility scripts for setup and maintenance
- `/test`: Test files

## 📊 Database Schema

The application uses a PostgreSQL database with the following key tables:

- `compliance_documents`: Stores documents for compliance checking
- `compliance_rules`: Defines compliance rules and patterns
- `kyc_profiles`: Stores KYC profiles with encrypted personal information
- `compliance_violations`: Records violations found during compliance checks
- `regulatory_updates`: Tracks regulatory changes and updates
- `workflow_executions`: Monitors DBOS workflow executions
- `audit_logs`: Maintains comprehensive audit trails

For detailed schema information, see [DATABASE_SCHEMA.md](DATABASE_SCHEMA.md).

## 🔄 API Endpoints

The application provides RESTful API endpoints for:

- Document management (`/api/documents/*`)
- KYC processing (`/api/kyc/*`)
- Compliance reporting (`/api/reports/*`)
- Regulatory updates (`/api/regulatory/*`)
- Dashboard analytics (`/api/dashboard/*`)
- Workflow management (`/api/workflows/*`)

## 🧪 Testing

Run tests with:

```bash
npm test
# or
bun test
```

For testing SQL syntax:

```bash
npm run test-sql
```

## 📝 Development Notes

### Phase 2 Implementation

The project is currently transitioning from static mock data to database-driven data. See [PHASE_2_IMPLEMENTATION_GUIDE.md](PHASE_2_IMPLEMENTATION_GUIDE.md) for details on the remaining implementation tasks.

### Refactoring Summary

For information about the refactoring from static mock data to database queries, see [REFACTORING_SUMMARY.md](REFACTORING_SUMMARY.md).

## 🔒 Security Features

- Personal information in KYC profiles is encrypted using pgcrypto
- Comprehensive audit logging for all critical operations
- Role-based access control for different user types
- Secure document handling and storage

## 🛠️ Troubleshooting

For common setup and configuration issues, see [SETUP_TROUBLESHOOTING.md](SETUP_TROUBLESHOOTING.md).

## 📈 Performance Considerations

- Database indexes are provided for frequently queried columns
- Query optimization for high-volume operations
- Caching strategies for frequently accessed data

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
