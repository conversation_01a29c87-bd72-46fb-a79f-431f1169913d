// Simple test to check if the server can start
import { spawn } from 'child_process';

console.log('Testing server startup...');

const server = spawn('npm', ['run', 'dev'], {
  stdio: 'pipe',
  cwd: process.cwd()
});

let output = '';
let hasError = false;

server.stdout.on('data', (data) => {
  const text = data.toString();
  output += text;
  console.log('STDOUT:', text);
  
  // Check for successful startup
  if (text.includes('Server running') || text.includes('listening') || text.includes('started')) {
    console.log('✅ Server started successfully!');
    server.kill();
    process.exit(0);
  }
});

server.stderr.on('data', (data) => {
  const text = data.toString();
  output += text;
  console.log('STDERR:', text);
  
  // Check for specific errors
  if (text.includes('Cannot find package') || text.includes('MODULE_NOT_FOUND')) {
    hasError = true;
    console.log('❌ Module not found error detected');
  }
});

server.on('close', (code) => {
  console.log(`Server process exited with code ${code}`);
  if (hasError) {
    console.log('❌ Server failed to start due to missing dependencies');
    process.exit(1);
  } else if (code === 0) {
    console.log('✅ Server started and stopped cleanly');
    process.exit(0);
  } else {
    console.log('❌ Server exited with error code');
    process.exit(1);
  }
});

// Timeout after 30 seconds
setTimeout(() => {
  console.log('⏰ Timeout reached, killing server...');
  server.kill();
  
  if (hasError) {
    console.log('❌ Server failed to start within timeout period');
    process.exit(1);
  } else {
    console.log('⚠️ Server did not start within timeout period');
    process.exit(1);
  }
}, 30000);
