#!/bin/bash

# Quick Setup for Compliance Command Center DBOS Database
# This script handles the most common permission issues automatically

set -e

DB_NAME="${DB_NAME:-dbos_kyc_demo}"
DB_USER="${DB_USER:-dbosadmin}"

echo "🚀 Quick Database Setup for Compliance Command Center"
echo "=================================================="
echo "Database: $DB_NAME"
echo "User: $DB_USER"
echo ""

# Check if we can connect as postgres user
if sudo -u postgres psql -c '\q' 2>/dev/null; then
    echo "✅ Found postgres superuser access"
    
    # Create database and grant permissions as postgres user
    echo "📝 Creating database and setting permissions..."
    sudo -u postgres psql << EOF
-- Create database if it doesn't exist
SELECT 'CREATE DATABASE $DB_NAME'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = '$DB_NAME')\gexec

-- Connect to the database
\c $DB_NAME

-- Grant permissions to current user
GRANT CREATE ON SCHEMA public TO $DB_USER;
GRANT USAGE ON SCHEMA public TO $DB_USER;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO $DB_USER;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO $DB_USER;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO $DB_USER;

-- Verify permissions
SELECT 'Permissions granted to $DB_USER' as status;
EOF

    echo "✅ Database and permissions set up successfully"
    
elif psql postgres -c '\q' 2>/dev/null; then
    echo "✅ Found direct postgres access"
    
    # Create database and grant permissions directly
    echo "📝 Creating database and setting permissions..."
    psql postgres << EOF
-- Create database if it doesn't exist
SELECT 'CREATE DATABASE $DB_NAME'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = '$DB_NAME')\gexec

-- Connect to the database
\c $DB_NAME

-- Grant permissions to current user
GRANT CREATE ON SCHEMA public TO $DB_USER;
GRANT USAGE ON SCHEMA public TO $DB_USER;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO $DB_USER;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO $DB_USER;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO $DB_USER;

-- Verify permissions
SELECT 'Permissions granted to $DB_USER' as status;
EOF

    echo "✅ Database and permissions set up successfully"
    
else
    echo "❌ Cannot access PostgreSQL as superuser"
    echo ""
    echo "Please run one of the following commands first:"
    echo ""
    echo "Option 1 (if you have sudo access):"
    echo "  sudo -u postgres psql -c \"GRANT CREATE ON SCHEMA public TO $DB_USER;\""
    echo ""
    echo "Option 2 (if you have postgres user password):"
    echo "  psql -U postgres -c \"GRANT CREATE ON SCHEMA public TO $DB_USER;\""
    echo ""
    echo "Option 3 (create database as postgres user):"
    echo "  sudo -u postgres createdb $DB_NAME"
    echo "  sudo -u postgres psql $DB_NAME -c \"GRANT CREATE ON SCHEMA public TO $DB_USER;\""
    echo ""
    exit 1
fi

# Apply the schema
echo "📊 Applying database schema..."
if [[ -f "database_schema.sql" ]]; then
    psql "$DB_NAME" -f database_schema.sql
    echo "✅ Schema applied successfully"
elif [[ -f "minimal_schema.sql" ]]; then
    echo "⚠️  Using minimal schema (database_schema.sql not found)"
    psql "$DB_NAME" -f minimal_schema.sql
    echo "✅ Minimal schema applied successfully"
else
    echo "❌ No schema file found (database_schema.sql or minimal_schema.sql)"
    exit 1
fi

# Verify installation
echo "🔍 Verifying installation..."
TABLE_COUNT=$(psql "$DB_NAME" -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_type = 'BASE TABLE';" | tr -d ' ')

if [[ $TABLE_COUNT -gt 5 ]]; then
    echo "✅ Installation verified: $TABLE_COUNT tables created"
else
    echo "❌ Installation verification failed: only $TABLE_COUNT tables found"
    exit 1
fi

# Display connection info
echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "Database URL for DBOS:"
echo "  postgresql://$DB_USER@localhost:5432/$DB_NAME"
echo ""
echo "Set this environment variable:"
echo "  export DBOS_DATABASE_URL=\"postgresql://$DB_USER@localhost:5432/$DB_NAME\""
echo ""
echo "Test the connection:"
echo "  psql $DB_NAME -c \"SELECT COUNT(*) FROM compliance_rules;\""
echo ""
