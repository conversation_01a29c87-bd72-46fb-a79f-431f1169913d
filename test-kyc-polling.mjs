#!/usr/bin/env node

// Test script to verify KYC submission and polling behavior
import fetch from 'node-fetch';

const API_BASE_URL = 'http://localhost:3000';

async function testKYCPolling() {
  console.log('🧪 Testing KYC submission and polling behavior...\n');

  try {
    // 1. Submit a KYC profile
    console.log('1. Submitting KYC profile...');
    const kycProfile = {
      customerId: `CUST-${Date.now()}`,
      personalInfo: {
        name: 'Test Customer',
        dateOfBirth: '1990-01-01',
        ssn: '***********',
        address: '123 Test Street, Test City, TS 12345'
      },
      riskScore: 0,
      status: 'pending',
      lastUpdated: new Date()
    };

    const submitResponse = await fetch(`${API_BASE_URL}/api/kyc/customer`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(kycProfile),
    });

    if (!submitResponse.ok) {
      throw new Error(`Failed to submit KYC: ${submitResponse.statusText}`);
    }

    const submitResult = await submitResponse.json();
    console.log('✅ KYC submitted successfully!');
    console.log(`   Workflow ID: ${submitResult.workflowId}`);
    console.log(`   KYC ID: ${submitResult.kycId}`);
    console.log(`   Status: ${submitResult.status}\n`);

    // 2. Poll workflow status and track polling behavior
    console.log('2. Monitoring workflow status and polling behavior...');
    const workflowId = submitResult.workflowId;
    let completed = false;
    let attempts = 0;
    const maxAttempts = 30; // 30 attempts * 3 seconds = 90 seconds max
    let lastStatus = '';

    while (!completed && attempts < maxAttempts) {
      attempts++;
      
      try {
        const statusResponse = await fetch(`${API_BASE_URL}/api/workflow/${workflowId}/status`);
        
        if (statusResponse.ok) {
          const statusResult = await statusResponse.json();
          
          // Only log if status changed
          if (statusResult.status !== lastStatus) {
            console.log(`   📊 Status changed: ${lastStatus} → ${statusResult.status}`);
            lastStatus = statusResult.status;
          }
          
          if (statusResult.events && statusResult.events.kyc_status) {
            console.log(`   🔄 Current step: ${statusResult.events.kyc_status}`);
          }
          
          if (statusResult.status === 'completed' || statusResult.status === 'failed') {
            completed = true;
            console.log(`\n🎉 Workflow completed with status: ${statusResult.status}`);
            
            // 3. Get final result
            console.log('3. Getting workflow result...');
            try {
              const resultResponse = await fetch(`${API_BASE_URL}/api/workflow/${workflowId}/result`);
              if (resultResponse.ok) {
                const result = await resultResponse.json();
                console.log('✅ Final result:');
                console.log(`   Status: ${result.result?.status || 'Unknown'}`);
                console.log(`   Risk Score: ${result.result?.riskScore || 'Unknown'}`);
                console.log(`   Reasons: ${result.result?.reasons?.join(', ') || 'None'}`);
              }
            } catch (resultError) {
              console.log('⚠️  Could not get workflow result:', resultError.message);
            }
            
            // 4. Test that polling should stop now
            console.log('\n4. Testing that polling stops after completion...');
            console.log('   Making 3 more status requests to verify no more processing...');
            
            for (let i = 1; i <= 3; i++) {
              await new Promise(resolve => setTimeout(resolve, 2000));
              const testResponse = await fetch(`${API_BASE_URL}/api/workflow/${workflowId}/status`);
              if (testResponse.ok) {
                const testResult = await testResponse.json();
                console.log(`   Test ${i}: Status still ${testResult.status} (should remain completed/failed)`);
              }
            }
            
            break;
          } else {
            console.log(`   ⏳ Attempt ${attempts}: Status = ${statusResult.status}`);
          }
        } else {
          console.log(`   ❌ Attempt ${attempts}: Failed to get status (${statusResponse.status})`);
        }
      } catch (statusError) {
        console.log(`   ❌ Attempt ${attempts}: Error getting status:`, statusError.message);
      }
      
      // Wait 3 seconds before next attempt
      await new Promise(resolve => setTimeout(resolve, 3000));
    }

    if (!completed) {
      console.log('⚠️  Workflow did not complete within the timeout period');
    }

    // 5. Check KYC queue to see if the new record appears
    console.log('\n5. Checking KYC queue for new record...');
    const queueResponse = await fetch(`${API_BASE_URL}/api/kyc/queue`);
    
    if (queueResponse.ok) {
      const queue = await queueResponse.json();
      const newRecord = queue.find(record => record.customerName === 'Test Customer');
      
      if (newRecord) {
        console.log('✅ New KYC record found in queue:');
        console.log(`   ID: ${newRecord.id}`);
        console.log(`   Customer: ${newRecord.customerName}`);
        console.log(`   Status: ${newRecord.status}`);
        console.log(`   Risk Score: ${newRecord.riskScore}`);
        console.log(`   Completed Steps: ${newRecord.completedSteps}/${newRecord.totalSteps}`);
        console.log(`   Flags: ${newRecord.flags.join(', ') || 'None'}`);
      } else {
        console.log('⚠️  New KYC record not found in queue');
      }
    } else {
      console.log('⚠️  Failed to fetch KYC queue');
    }

    console.log('\n🎉 Test completed successfully!');
    console.log('📝 Summary:');
    console.log(`   - Workflow completed in ${attempts} polling attempts`);
    console.log(`   - Final status: ${lastStatus}`);
    console.log(`   - Polling should now stop automatically`);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
testKYCPolling();
