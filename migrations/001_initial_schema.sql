-- Migration: 001_initial_schema.sql
-- Description: Initial database schema for Compliance Command Center DBOS
-- Created: 2024-01-09
-- Author: System

-- This migration creates the initial database schema for the compliance system
-- Run this file against a PostgreSQL database to set up all required tables

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- <PERSON><PERSON> <PERSON>NU<PERSON> types
CREATE TYPE document_type_enum AS ENUM ('contract', 'policy', 'procedure', 'financial_report');
CREATE TYPE document_status_enum AS ENUM ('pending', 'processing', 'compliant', 'non_compliant', 'requires_review');
CREATE TYPE compliance_standard_enum AS ENUM ('SEC', 'GLBA', 'SOX', 'GDPR', 'CCPA', 'FINRA');
CREATE TYPE rule_type_enum AS ENUM ('data_protection', 'financial_disclosure', 'privacy', 'security');
CREATE TYPE severity_enum AS ENUM ('low', 'medium', 'high', 'critical');
CREATE TYPE kyc_status_enum AS ENUM ('pending', 'approved', 'rejected', 'under_review');
CREATE TYPE report_type_enum AS ENUM ('monthly', 'quarterly', 'annual', 'incident');
CREATE TYPE impact_level_enum AS ENUM ('low', 'medium', 'high');

-- Create core tables
\i database_schema.sql

-- Insert initial configuration data
INSERT INTO compliance_standards_config (standard, display_name, description, monitoring_enabled, notification_enabled) VALUES
('SEC', 'Securities and Exchange Commission', 'U.S. federal agency that enforces securities laws', true, true),
('GLBA', 'Gramm-Leach-Bliley Act', 'Financial privacy rule for financial institutions', true, true),
('SOX', 'Sarbanes-Oxley Act', 'Federal law for public company auditing and financial regulations', true, true),
('GDPR', 'General Data Protection Regulation', 'EU regulation on data protection and privacy', true, true),
('CCPA', 'California Consumer Privacy Act', 'California privacy rights statute', true, true),
('FINRA', 'Financial Industry Regulatory Authority', 'Self-regulatory organization for brokerage firms', true, true)
ON CONFLICT (standard) DO NOTHING;

-- Insert default compliance rules
INSERT INTO compliance_rules (rule_id, standard, rule_type, description, pattern, severity, is_active) VALUES
('SEC-001', 'SEC', 'financial_disclosure', 'Financial statements must include quarterly earnings disclosure', 'quarterly.*(earnings|revenue|income)', 'high', true),
('GLBA-001', 'GLBA', 'privacy', 'Customer financial information must be protected', '(ssn|social.security|account.number|routing.number)', 'critical', true),
('SOX-001', 'SOX', 'financial_disclosure', 'Internal controls must be documented', 'internal.control.*documentation', 'high', true),
('GDPR-001', 'GDPR', 'data_protection', 'Personal data processing must have legal basis', '(personal.data|processing|consent)', 'high', true),
('CCPA-001', 'CCPA', 'privacy', 'Consumer privacy rights must be disclosed', '(consumer.rights|privacy.policy|data.sale)', 'medium', true)
ON CONFLICT (rule_id) DO NOTHING;

-- Insert system configuration
INSERT INTO system_configuration (config_key, config_value, config_type, description, category) VALUES
('compliance.auto_scan_enabled', 'true', 'boolean', 'Enable automatic compliance scanning', 'compliance'),
('kyc.risk_threshold_high', '70', 'number', 'Risk score threshold for high-risk profiles', 'kyc'),
('kyc.risk_threshold_critical', '85', 'number', 'Risk score threshold for critical-risk profiles', 'kyc'),
('reporting.auto_generation_enabled', 'true', 'boolean', 'Enable automatic report generation', 'reporting'),
('notifications.email_enabled', 'true', 'boolean', 'Enable email notifications', 'system'),
('workflow.max_retry_attempts', '3', 'number', 'Maximum retry attempts for workflows', 'system'),
('compliance.violation_retention_days', '2555', 'number', 'Violation records retention (7 years)', 'compliance'),
('audit.log_retention_days', '2555', 'number', 'Audit logs retention (7 years)', 'system')
ON CONFLICT (config_key) DO NOTHING;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_compliance_documents_status ON compliance_documents(status);
CREATE INDEX IF NOT EXISTS idx_compliance_documents_type ON compliance_documents(document_type);
CREATE INDEX IF NOT EXISTS idx_compliance_documents_uploaded_at ON compliance_documents(uploaded_at);
CREATE INDEX IF NOT EXISTS idx_compliance_violations_severity ON compliance_violations(severity);
CREATE INDEX IF NOT EXISTS idx_compliance_violations_detected_at ON compliance_violations(detected_at);
CREATE INDEX IF NOT EXISTS idx_kyc_profiles_status ON kyc_profiles(status);
CREATE INDEX IF NOT EXISTS idx_kyc_profiles_risk_score ON kyc_profiles(risk_score);
CREATE INDEX IF NOT EXISTS idx_workflow_executions_status ON workflow_executions(status);
CREATE INDEX IF NOT EXISTS idx_audit_logs_occurred_at ON audit_logs(occurred_at);

-- Verify installation
DO $$
DECLARE
    table_count INTEGER;
    enum_count INTEGER;
BEGIN
    -- Count tables
    SELECT COUNT(*) INTO table_count
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_type = 'BASE TABLE';
    
    -- Count enums
    SELECT COUNT(*) INTO enum_count
    FROM pg_type 
    WHERE typtype = 'e';
    
    RAISE NOTICE 'Migration completed successfully!';
    RAISE NOTICE 'Created % tables and % enum types', table_count, enum_count;
    RAISE NOTICE 'Database schema is ready for Compliance Command Center DBOS';
END $$;
