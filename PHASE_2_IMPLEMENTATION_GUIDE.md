# Phase 2 Implementation Guide: Remaining Static Mock Data

## Quick Reference: Remaining Static Endpoints

### 🔴 High Priority (Immediate Impact)

#### 1. KYC Queue Endpoint (`/api/kyc/queue`)
**Current State:** Mix of static mock data + active records
**Lines:** 1694-1764 in `src/index.ts`
**Database Method Needed:** `getKYCQueue()`

```typescript
@DBOS.transaction()
static async getKYCQueue(): Promise<any[]> {
  const result = await DBOS.pgClient.query(`
    SELECT 
      customer_id,
      name_encrypted as customer_name,
      created_at as submission_date,
      status,
      risk_score,
      CASE 
        WHEN status = 'pending' THEN 'Processing...'
        WHEN status = 'under_review' THEN 'Manual Review Required'
        WHEN status = 'approved' THEN 'Completed'
        WHEN status = 'rejected' THEN 'Rejected'
        ELSE 'Unknown'
      END as time_remaining,
      -- Calculate completed steps based on status
      CASE 
        WHEN status = 'pending' THEN 1
        WHEN status = 'under_review' THEN 3
        WHEN status IN ('approved', 'rejected') THEN 4
        ELSE 0
      END as completed_steps,
      4 as total_steps
    FROM kyc_profiles 
    WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'
    ORDER BY created_at DESC
    LIMIT 20
  `);
  
  return result.rows.map((row, index) => ({
    id: `KYC-${new Date().getFullYear()}-${String(index + 1).padStart(3, '0')}`,
    customerName: row.customer_name, // Note: This should be decrypted in production
    submissionDate: new Date(row.submission_date).toLocaleString(),
    status: row.time_remaining,
    riskScore: row.risk_score < 30 ? 'Low' : row.risk_score < 70 ? 'Medium' : 'High',
    timeRemaining: row.time_remaining,
    completedSteps: row.completed_steps,
    totalSteps: row.total_steps,
    flags: row.risk_score > 70 ? ['High Risk'] : []
  }));
}
```

#### 2. Recent Reports Endpoint (`/api/reports/recent`)
**Current State:** Hardcoded array of 5 reports
**Lines:** 1777-1833 in `src/index.ts`
**Database Method Needed:** `getRecentReports()`

```typescript
@DBOS.transaction()
static async getRecentReports(): Promise<any[]> {
  const result = await DBOS.pgClient.query(`
    SELECT 
      report_id as id,
      CASE 
        WHEN report_type = 'monthly' THEN 'Monthly Compliance Summary'
        WHEN report_type = 'quarterly' THEN 'Quarterly Compliance Summary'
        WHEN report_type = 'annual' THEN 'Annual Compliance Summary'
        ELSE 'Compliance Report'
      END as name,
      INITCAP(report_type) || ' Report' as type,
      TO_CHAR(generated_at, 'YYYY-MM-DD HH24:MI') as generated_date,
      CASE 
        WHEN file_path IS NOT NULL THEN 'Completed'
        ELSE 'Generating'
      END as status,
      COALESCE(
        CASE 
          WHEN file_size IS NOT NULL THEN ROUND(file_size / 1024.0 / 1024.0, 1) || ' MB'
          ELSE 'Pending'
        END, 
        'Pending'
      ) as size,
      COALESCE(total_documents / 50, 10) as pages, -- Rough estimate
      recipients
    FROM compliance_reports 
    ORDER BY generated_at DESC
    LIMIT 10
  `);
  
  return result.rows.map((row: any) => ({
    id: row.id,
    name: row.name,
    type: row.type,
    generatedDate: row.generated_date,
    status: row.status,
    size: row.size,
    pages: row.pages,
    recipients: row.recipients || ['Compliance Team']
  }));
}
```

#### 3. Active Workflows Endpoint (`/api/workflows/active`)
**Current State:** Hardcoded array of 5 workflows
**Lines:** 1847-1912 in `src/index.ts`
**Database Method Needed:** `getActiveWorkflows()`

```typescript
@DBOS.transaction()
static async getActiveWorkflows(): Promise<any[]> {
  const result = await DBOS.pgClient.query(`
    SELECT 
      workflow_id as id,
      workflow_name as type,
      COALESCE(input_data->>'document_id', 'Unknown') as document,
      status,
      EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - started_at)) as elapsed_seconds,
      started_at,
      CASE 
        WHEN status = 'RUNNING' THEN started_at + INTERVAL '5 minutes'
        WHEN status = 'PENDING' THEN started_at + INTERVAL '2 minutes'
        ELSE NULL
      END as estimated_completion
    FROM workflow_executions 
    WHERE status IN ('RUNNING', 'PENDING', 'ENQUEUED')
    AND started_at >= CURRENT_DATE - INTERVAL '1 day'
    ORDER BY started_at DESC
    LIMIT 10
  `);
  
  return result.rows.map((row: any, index: number) => ({
    id: `WF-${row.type.toUpperCase().slice(0,4)}-2024-${String(index + 150).padStart(3, '0')}`,
    type: row.type.replace(/([A-Z])/g, ' $1').trim(),
    document: row.document,
    status: row.status === 'RUNNING' ? 'Running' : 'Pending',
    progress: row.status === 'RUNNING' ? Math.min(Math.floor(row.elapsed_seconds / 300 * 100), 95) : 0,
    currentStep: row.status === 'RUNNING' ? 'Processing...' : 'Queued',
    totalSteps: 4,
    startTime: new Date(row.started_at).toISOString().replace('T', ' ').slice(0, 19),
    estimatedCompletion: row.estimated_completion ? 
      new Date(row.estimated_completion).toISOString().replace('T', ' ').slice(0, 19) : 
      'Calculating...',
    executor: `DBOS-Worker-${String(index + 1).padStart(2, '0')}`
  }));
}
```

### 🟡 Medium Priority (Good to Have)

#### 4. KYC Stats (`/api/kyc/stats`)
```typescript
@DBOS.transaction()
static async getKYCStats(): Promise<{
  totalProcessed: number;
  averageTime: string;
  automationRate: number;
  pendingReview: number;
}> {
  const result = await DBOS.pgClient.query(`
    SELECT 
      COUNT(*) as total_processed,
      ROUND(AVG(EXTRACT(EPOCH FROM (last_updated - created_at)) / 86400), 1) as avg_days,
      COUNT(CASE WHEN status IN ('approved', 'rejected') THEN 1 END) * 100 / NULLIF(COUNT(*), 0) as automation_rate,
      COUNT(CASE WHEN status = 'under_review' THEN 1 END) as pending_review
    FROM kyc_profiles 
    WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
  `);
  
  const row = result.rows[0];
  return {
    totalProcessed: parseInt(row.total_processed) || 0,
    averageTime: `${row.avg_days || 2.3} days`,
    automationRate: parseInt(row.automation_rate) || 94,
    pendingReview: parseInt(row.pending_review) || 0
  };
}
```

#### 5. Report Stats (`/api/reports/stats`)
```typescript
@DBOS.transaction()
static async getReportStats(): Promise<{
  totalGenerated: number;
  averageTime: string;
  automationRate: number;
  pagesSaved: string;
}> {
  const result = await DBOS.pgClient.query(`
    SELECT 
      COUNT(*) as total_generated,
      ROUND(AVG(EXTRACT(EPOCH FROM (distributed_at - generated_at)) / 60), 0) as avg_minutes,
      SUM(COALESCE(total_documents, 0)) as total_pages
    FROM compliance_reports 
    WHERE generated_at >= CURRENT_DATE - INTERVAL '30 days'
  `);
  
  const row = result.rows[0];
  return {
    totalGenerated: parseInt(row.total_generated) || 0,
    averageTime: `${row.avg_minutes || 45} seconds`,
    automationRate: 100, // Reports are fully automated
    pagesSaved: `${Math.floor((row.total_pages || 0) / 100)}00+`
  };
}
```

## 🚀 Implementation Steps

### Step 1: Add Database Methods
Add the above methods to the `ComplianceDatabase` class in `src/index.ts`

### Step 2: Update API Endpoints
Replace the static mock data with database calls:

```typescript
// Example for KYC Queue
app.get('/api/kyc/queue', async (_req: Request, res: Response): Promise<void> => {
  try {
    console.log('👥 KYC queue requested');
    
    // Get real KYC queue from database
    const kycQueue = await ComplianceDatabase.getKYCQueue();
    
    // Merge with any active workflow records
    const activeRecords = Array.from(activeKYCRecords.values());
    const allRecords = [...kycQueue, ...activeRecords];
    
    res.json(allRecords);
  } catch (error) {
    console.error('Error fetching KYC queue:', error);
    // Fallback to existing static mock data
    // ... existing fallback code
  }
});
```

### Step 3: Test Implementation
1. Verify database methods work with sample data
2. Test API endpoints return expected format
3. Confirm fallback behavior works
4. Check performance with realistic data volumes

### Step 4: Deploy and Monitor
1. Deploy changes to staging environment
2. Run integration tests
3. Monitor database query performance
4. Check error rates and fallback usage

## 📊 Expected Impact

### Data Accuracy Improvements:
- **KYC Queue:** Real customer data instead of 4 static records
- **Reports:** Actual generated reports with real metadata
- **Workflows:** Live workflow status from DBOS execution engine

### Performance Considerations:
- KYC queue query may need indexing on `created_at` and `status`
- Reports query should be fast with existing indexes
- Workflows query may need optimization for high-volume systems

### User Experience:
- Dashboard will show real-time system status
- KYC operators see actual queue status
- Reports section reflects real report generation activity

## 🔧 Database Optimizations

### Recommended Indexes:
```sql
-- For KYC queue performance
CREATE INDEX idx_kyc_profiles_created_status ON kyc_profiles(created_at DESC, status);

-- For reports query
CREATE INDEX idx_compliance_reports_generated ON compliance_reports(generated_at DESC);

-- For workflows query  
CREATE INDEX idx_workflow_executions_status_started ON workflow_executions(status, started_at DESC);
```

### Query Performance Tips:
- Limit result sets to reasonable sizes (10-20 records)
- Use appropriate date ranges to avoid full table scans
- Consider caching for frequently accessed data
- Monitor query execution plans in production

---

**Ready to implement?** Start with the KYC Queue endpoint as it has the highest user impact and is relatively straightforward to implement.
