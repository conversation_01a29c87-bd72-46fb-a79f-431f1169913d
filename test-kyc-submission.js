#!/usr/bin/env node

// Test script to verify KYC submission and workflow completion
import fetch from 'node-fetch';

const API_BASE_URL = 'http://localhost:3000';

async function testKYCSubmission() {
  console.log('🧪 Testing KYC submission and workflow completion...\n');

  try {
    // 1. Submit a KYC profile
    console.log('1. Submitting KYC profile...');
    const kycProfile = {
      customerId: `CUST-${Date.now()}`,
      personalInfo: {
        name: 'Test Customer',
        dateOfBirth: '1990-01-01',
        ssn: '***********',
        address: '123 Test Street, Test City, TS 12345'
      },
      riskScore: 0,
      status: 'pending',
      lastUpdated: new Date()
    };

    const submitResponse = await fetch(`${API_BASE_URL}/api/kyc/customer`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(kycProfile),
    });

    if (!submitResponse.ok) {
      throw new Error(`Failed to submit KYC: ${submitResponse.statusText}`);
    }

    const submitResult = await submitResponse.json();
    console.log('✅ KYC submitted successfully!');
    console.log(`   Workflow ID: ${submitResult.workflowId}`);
    console.log(`   KYC ID: ${submitResult.kycId}`);
    console.log(`   Status: ${submitResult.status}\n`);

    // 2. Poll workflow status until completion
    console.log('2. Monitoring workflow status...');
    const workflowId = submitResult.workflowId;
    let completed = false;
    let attempts = 0;
    const maxAttempts = 30; // 30 attempts * 3 seconds = 90 seconds max

    while (!completed && attempts < maxAttempts) {
      attempts++;
      
      try {
        const statusResponse = await fetch(`${API_BASE_URL}/api/workflow/${workflowId}/status`);
        
        if (statusResponse.ok) {
          const statusResult = await statusResponse.json();
          console.log(`   Attempt ${attempts}: Status = ${statusResult.status}`);
          
          if (statusResult.events && statusResult.events.kyc_status) {
            console.log(`   Current step: ${statusResult.events.kyc_status}`);
          }
          
          if (statusResult.status === 'completed' || statusResult.status === 'failed') {
            completed = true;
            console.log('✅ Workflow completed!\n');
            
            // 3. Get final result
            console.log('3. Getting workflow result...');
            try {
              const resultResponse = await fetch(`${API_BASE_URL}/api/workflow/${workflowId}/result`);
              if (resultResponse.ok) {
                const result = await resultResponse.json();
                console.log('✅ Final result:');
                console.log(`   Status: ${result.result?.status || 'Unknown'}`);
                console.log(`   Risk Score: ${result.result?.riskScore || 'Unknown'}`);
                console.log(`   Reasons: ${result.result?.reasons?.join(', ') || 'None'}\n`);
              }
            } catch (resultError) {
              console.log('⚠️  Could not get workflow result:', resultError.message);
            }
            
            break;
          }
        } else {
          console.log(`   Attempt ${attempts}: Failed to get status (${statusResponse.status})`);
        }
      } catch (statusError) {
        console.log(`   Attempt ${attempts}: Error getting status:`, statusError.message);
      }
      
      // Wait 3 seconds before next attempt
      await new Promise(resolve => setTimeout(resolve, 3000));
    }

    if (!completed) {
      console.log('⚠️  Workflow did not complete within the timeout period');
    }

    // 4. Check KYC queue to see if the new record appears
    console.log('4. Checking KYC queue...');
    const queueResponse = await fetch(`${API_BASE_URL}/api/kyc/queue`);
    
    if (queueResponse.ok) {
      const queue = await queueResponse.json();
      const newRecord = queue.find(record => record.customerName === 'Test Customer');
      
      if (newRecord) {
        console.log('✅ New KYC record found in queue:');
        console.log(`   ID: ${newRecord.id}`);
        console.log(`   Customer: ${newRecord.customerName}`);
        console.log(`   Status: ${newRecord.status}`);
        console.log(`   Risk Score: ${newRecord.riskScore}`);
        console.log(`   Completed Steps: ${newRecord.completedSteps}/${newRecord.totalSteps}`);
        console.log(`   Flags: ${newRecord.flags.join(', ') || 'None'}`);
      } else {
        console.log('⚠️  New KYC record not found in queue');
      }
    } else {
      console.log('⚠️  Failed to fetch KYC queue');
    }

    console.log('\n🎉 Test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
testKYCSubmission();
