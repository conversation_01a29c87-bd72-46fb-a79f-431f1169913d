# Database Schema Documentation

## Overview

This document describes the PostgreSQL database schema for the Compliance Command Center DBOS application. The schema is designed to support all data structures and workflows defined in `src/index.ts`.

## Quick Setup

### Prerequisites
- PostgreSQL 12+ with UUID and pgcrypto extensions
- Database user with CREATE privileges

### Installation
```bash
# Create database
createdb compliance_command_center

# Run the schema
psql compliance_command_center -f database_schema.sql

# Or use the migration file
psql compliance_command_center -f migrations/001_initial_schema.sql
```

## Schema Overview

### Core Tables

#### 1. `compliance_documents`
Stores documents uploaded for compliance checking.

**Key Fields:**
- `document_id`: External document identifier
- `content`: Document text content
- `document_type`: contract, policy, procedure, financial_report
- `status`: pending, processing, compliant, non_compliant, requires_review
- `metadata`: Additional document information (JSONB)

#### 2. `compliance_rules`
Stores compliance rules used for document scanning.

**Key Fields:**
- `rule_id`: External rule identifier (e.g., "SEC-001")
- `standard`: SEC, GLBA, SOX, GDPR, CCPA, FINRA
- `pattern`: Regex pattern for violation detection
- `severity`: low, medium, high, critical

#### 3. `kyc_profiles`
Stores Know Your Customer profiles with encrypted personal information.

**Key Fields:**
- `customer_id`: External customer identifier
- `*_encrypted`: Encrypted personal information fields
- `risk_score`: 0-100 risk assessment score
- `status`: pending, approved, rejected, under_review

#### 4. `compliance_violations`
Stores violations found during compliance checks.

**Key Fields:**
- `document_id`: Reference to compliance_documents
- `rule_id`: Reference to compliance_rules
- `severity`: Violation severity level
- `confidence_score`: AI confidence in violation (0.00-1.00)

#### 5. `regulatory_updates`
Stores regulatory updates and changes.

**Key Fields:**
- `standard`: Regulatory standard
- `impact`: low, medium, high
- `action_required`: Boolean flag
- `recommended_actions`: Array of recommended actions

### Workflow & Audit Tables

#### 6. `workflow_executions`
Tracks DBOS workflow executions and status.

#### 7. `workflow_events`
Stores workflow events and progress tracking.

#### 8. `audit_logs`
Comprehensive audit trail for all system activities.

### System Tables

#### 9. `compliance_reports`
Stores generated compliance reports.

#### 10. `notifications`
System notifications and alerts.

#### 11. `performance_metrics`
System performance and compliance metrics.

#### 12. `system_configuration`
System-wide configuration settings.

#### 13. `compliance_standards_config`
Configuration for different compliance standards.

## Data Types

### Custom ENUM Types
- `document_type_enum`: Document categories
- `document_status_enum`: Document processing status
- `compliance_standard_enum`: Regulatory standards
- `severity_enum`: Risk/violation severity levels
- `kyc_status_enum`: KYC processing status
- `report_type_enum`: Report categories
- `impact_level_enum`: Impact assessment levels

## Security Features

### Data Encryption
- Personal information in KYC profiles is encrypted using pgcrypto
- Utility functions provided for encryption/decryption
- Sensitive configuration marked with `is_sensitive` flag

### Audit Trail
- All critical operations logged in `audit_logs`
- Automatic timestamp updates via triggers
- Change tracking with old/new values

## Performance Optimizations

### Indexes
- Strategic indexes on frequently queried columns
- Composite indexes for complex queries
- Time-based indexes for audit and metrics tables

### Views
- `compliance_dashboard_summary`: Dashboard metrics
- `kyc_dashboard_summary`: KYC statistics

## Utility Functions

### `calculate_compliance_rate()`
Calculates compliance rate for a given period and document type.

### `encrypt_sensitive_data()` / `decrypt_sensitive_data()`
Handles encryption/decryption of sensitive information.

## Sample Data

The schema includes sample data for:
- Compliance standards configuration
- Default compliance rules (SEC-001, GLBA-001, SOX-001, etc.)
- System configuration settings

## Integration with DBOS

### Workflow Tracking
- `workflow_executions` table tracks all DBOS workflows
- `workflow_events` stores workflow progress and events
- Supports workflow types: compliance_check, kyc_process, report_generation

### Configuration
- Database URL configured via `DBOS_DATABASE_URL` environment variable
- DBOS system name: "regulatory-compliance-system"

## Maintenance

### Data Retention
- Violation records: 7 years (configurable)
- Audit logs: 7 years (configurable)
- Performance metrics: Configurable by aggregation level

### Backup Recommendations
- Daily backups of all tables
- Point-in-time recovery enabled
- Encrypted backups for compliance data

## Migration Strategy

### Version Control
- Migrations stored in `migrations/` directory
- Sequential numbering: `001_initial_schema.sql`, `002_add_feature.sql`
- Each migration includes rollback instructions

### Deployment
1. Test migration on staging environment
2. Backup production database
3. Apply migration during maintenance window
4. Verify data integrity
5. Update application configuration if needed

## Troubleshooting

### Common Issues
1. **Extension not found**: Ensure uuid-ossp and pgcrypto extensions are available
2. **Permission denied**: Verify database user has required privileges
3. **Enum conflicts**: Drop existing enums before recreating

### Performance Monitoring
- Monitor query performance on indexed columns
- Watch for table bloat on high-update tables
- Regular VACUUM and ANALYZE operations recommended

## Support

For questions about the database schema:
1. Check this documentation
2. Review the source code in `src/index.ts`
3. Examine the schema file `database_schema.sql`
4. Check migration files in `migrations/` directory
