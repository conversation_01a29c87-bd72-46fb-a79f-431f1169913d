# 🚀 Mock Data Setup Guide

This guide will help you load all the static data from `src/index.ts` into your database.

## 📋 Prerequisites

### 1. Install Dependencies
```bash
npm install
```
This will install the `pg` PostgreSQL client library that was added to package.json.

### 2. Database Setup
Ensure PostgreSQL is running and the database schema is applied:

```bash
# Create database (if it doesn't exist)
createdb dbos_kyc_demo

# Apply the schema
psql -d dbos_kyc_demo -f database_schema.sql
```

### 3. Environment Configuration
Set your database connection details using one of these methods:

#### Option A: Connection String (Recommended)
```bash
export DBOS_DATABASE_URL="postgresql://username:password@localhost:5432/dbos_kyc_demo"
```

#### Option B: Individual Variables
```bash
export PGHOST=localhost
export PGPORT=5432
export PGDATABASE=dbos_kyc_demo
export PGUSER=postgres
export PGPASSWORD=your_password
```

## 🎯 Quick Start

### Step 1: Validate SQL Syntax
```bash
npm run test-sql
```
This validates the SQL file without connecting to the database, including JSON syntax validation.

### Step 2: Load Mock Data
```bash
npm run load-mock-data
```
This loads all the mock data from `src/index.ts` into your database.

### Step 3: Start the Application
```bash
npm start
```
Your application will now have realistic data in all dashboard sections!

## 📊 What Gets Loaded

The script loads **50+ records** across **10 data categories**:

### Core Data
- ✅ **3 Compliance Rules** (SEC, GLBA, SOX)
- ✅ **2 Regulatory Updates** (with action requirements)
- ✅ **5 Sample Documents** (various compliance statuses)
- ✅ **7 KYC Profiles** (encrypted personal info)
- ✅ **3 Compliance Violations** (critical, high, medium)

### System Data
- ✅ **5 Compliance Reports** (quarterly, monthly, incident)
- ✅ **5 Workflow Executions** (running, completed, paused)
- ✅ **4 Notifications** (alerts, reviews, updates)
- ✅ **12 Performance Metrics** (compliance, KYC, system)
- ✅ **7 Audit Logs** (user actions, system events)

## 🔧 Advanced Usage

### Force Reload (Clear Existing Data)
```bash
npm run load-mock-data:force
```
⚠️ **Warning**: This clears existing data before loading!

### Verbose Output
```bash
node scripts/load_mock_data.js --verbose
```

### Help and Options
```bash
node scripts/load_mock_data.js --help
```

## 🛠️ Troubleshooting

### "Database schema not found"
```bash
# Apply the schema first
psql -d dbos_kyc_demo -f database_schema.sql
```

### "Connection refused"
```bash
# Check if PostgreSQL is running
pg_ctl status

# Test connection
psql -d dbos_kyc_demo -c "SELECT version();"
```

### "Permission denied"
```bash
# Grant permissions to your user
psql -d dbos_kyc_demo -c "GRANT ALL ON ALL TABLES IN SCHEMA public TO your_username;"
```

### "Module not found: pg"
```bash
# Install dependencies
npm install
```

## 🔍 Verification

After loading, verify the data was loaded correctly:

```sql
-- Check record counts
SELECT 
  'compliance_rules' as table_name, COUNT(*) as records FROM compliance_rules
UNION ALL
SELECT 'compliance_documents', COUNT(*) FROM compliance_documents
UNION ALL
SELECT 'kyc_profiles', COUNT(*) FROM kyc_profiles
UNION ALL
SELECT 'compliance_violations', COUNT(*) FROM compliance_violations;

-- Check sample data
SELECT rule_id, standard, severity FROM compliance_rules;
SELECT document_id, status, file_name FROM compliance_documents;
```

Expected output:
```
     table_name      | records 
--------------------+---------
 compliance_rules   |       6
 compliance_documents|       5
 kyc_profiles       |       7
 compliance_violations|      3
```

## 🎉 Success!

Once loaded, your application will have:

- **Populated Dashboard** with real metrics
- **Document Management** with sample files
- **KYC Queue** with customer profiles
- **Violation Tracking** with sample issues
- **Report History** with generated reports
- **Workflow Monitoring** with active processes
- **Audit Trail** with system activities

## 📁 Files Created

- `scripts/load_mock_data.sql` - Main SQL script (28.8 KB)
- `scripts/load_mock_data.js` - Node.js execution script
- `scripts/test_sql_syntax.js` - SQL validation script
- `scripts/README.md` - Detailed documentation
- `scripts/summary.md` - Implementation summary

## 🔐 Security Notes

- **Encrypted Data**: KYC personal information is encrypted using PostgreSQL's pgp_sym_encrypt
- **Demo Keys**: Uses 'demo_key' for development (change for production!)
- **Safe Execution**: ON CONFLICT clauses prevent duplicate data issues
- **SQL Injection Protection**: Parameterized queries and validation

## 🚀 Next Steps

1. **Load the data**: `npm run load-mock-data`
2. **Start the app**: `npm start`
3. **Explore the dashboard**: See all the loaded data in action
4. **Test workflows**: Trigger new compliance checks and KYC processes
5. **Customize**: Modify the scripts to add your own mock data

---

**Need help?** Check `scripts/README.md` for detailed documentation or run `node scripts/load_mock_data.js --help` for command options.
