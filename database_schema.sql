-- PostgreSQL Database Schema for Compliance Command Center DBOS
-- This schema supports all data structures used in src/index.ts

-- Enable UUID extension for generating unique identifiers
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Enable pgcrypto for encryption functions
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create custom ENUM types for better data integrity
CREATE TYPE document_type_enum AS ENUM ('contract', 'policy', 'procedure', 'financial_report');
CREATE TYPE document_status_enum AS ENUM ('pending', 'processing', 'compliant', 'non_compliant', 'requires_review');
CREATE TYPE compliance_standard_enum AS ENUM ('SEC', 'GLBA', 'SOX', 'GDPR', 'CCPA', 'FINRA');
CREATE TYPE rule_type_enum AS ENUM ('data_protection', 'financial_disclosure', 'privacy', 'security');
CREATE TYPE severity_enum AS ENUM ('low', 'medium', 'high', 'critical');
CREATE TYPE kyc_status_enum AS ENUM ('pending', 'approved', 'rejected', 'under_review');
CREATE TYPE report_type_enum AS ENUM ('monthly', 'quarterly', 'annual', 'incident');
CREATE TYPE impact_level_enum AS ENUM ('low', 'medium', 'high');

-- =====================================================
-- CORE COMPLIANCE TABLES
-- =====================================================

-- Table: compliance_documents
-- Stores documents that need compliance checking
CREATE TABLE compliance_documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    document_id VARCHAR(255) UNIQUE NOT NULL, -- External document identifier
    content TEXT NOT NULL,
    document_type document_type_enum NOT NULL,
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    status document_status_enum DEFAULT 'pending',
    file_name VARCHAR(500),
    file_size BIGINT,
    mime_type VARCHAR(100),
    checksum VARCHAR(64), -- SHA-256 hash for integrity
    uploaded_by VARCHAR(255),
    metadata JSONB, -- Additional document metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table: compliance_rules
-- Stores compliance rules used for document scanning
CREATE TABLE compliance_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    rule_id VARCHAR(100) UNIQUE NOT NULL, -- External rule identifier (e.g., "SEC-001")
    standard compliance_standard_enum NOT NULL,
    rule_type rule_type_enum NOT NULL,
    description TEXT NOT NULL,
    pattern TEXT NOT NULL, -- Regex pattern for matching
    severity severity_enum NOT NULL,
    is_active BOOLEAN DEFAULT true,
    effective_date DATE,
    expiry_date DATE,
    created_by VARCHAR(255),
    version INTEGER DEFAULT 1,
    metadata JSONB, -- Additional rule configuration
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table: kyc_profiles
-- Stores Know Your Customer profiles
CREATE TABLE kyc_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id VARCHAR(255) UNIQUE NOT NULL,
    -- Personal Information (encrypted)
    name_encrypted TEXT NOT NULL,
    date_of_birth_encrypted TEXT NOT NULL,
    ssn_encrypted TEXT NOT NULL,
    address_encrypted TEXT NOT NULL,
    -- Risk Assessment
    risk_score INTEGER CHECK (risk_score >= 0 AND risk_score <= 100),
    status kyc_status_enum DEFAULT 'pending',
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    -- Verification Details
    identity_verified BOOLEAN DEFAULT false,
    identity_confidence DECIMAL(3,2), -- 0.00 to 1.00
    sanctions_checked BOOLEAN DEFAULT false,
    sanctions_match BOOLEAN DEFAULT false,
    sanctions_details TEXT,
    -- Audit Trail
    created_by VARCHAR(255),
    approved_by VARCHAR(255),
    approved_at TIMESTAMP WITH TIME ZONE,
    rejected_by VARCHAR(255),
    rejected_at TIMESTAMP WITH TIME ZONE,
    rejection_reason TEXT,
    metadata JSONB, -- Additional KYC data
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table: compliance_violations
-- Stores violations found during compliance checks
CREATE TABLE compliance_violations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    document_id UUID NOT NULL REFERENCES compliance_documents(id) ON DELETE CASCADE,
    rule_id UUID NOT NULL REFERENCES compliance_rules(id) ON DELETE CASCADE,
    violation_type VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    severity severity_enum NOT NULL,
    recommended_action TEXT NOT NULL,
    detected_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    -- Resolution tracking
    status VARCHAR(50) DEFAULT 'open', -- open, in_progress, resolved, false_positive
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolved_by VARCHAR(255),
    resolution_notes TEXT,
    -- Context information
    match_text TEXT, -- The actual text that triggered the violation
    match_position INTEGER, -- Position in document where violation was found
    context_before TEXT, -- Text before the match
    context_after TEXT, -- Text after the match
    confidence_score DECIMAL(3,2), -- AI confidence in the violation (0.00 to 1.00)
    metadata JSONB, -- Additional violation data
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table: regulatory_updates
-- Stores regulatory updates and changes
CREATE TABLE regulatory_updates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    update_id VARCHAR(255) UNIQUE NOT NULL, -- External update identifier
    standard compliance_standard_enum NOT NULL,
    title VARCHAR(500) NOT NULL,
    description TEXT NOT NULL,
    effective_date DATE NOT NULL,
    impact impact_level_enum NOT NULL,
    action_required BOOLEAN DEFAULT false,
    -- Source information
    source_url TEXT,
    source_document VARCHAR(500),
    published_date DATE,
    -- Processing status
    processed BOOLEAN DEFAULT false,
    processed_at TIMESTAMP WITH TIME ZONE,
    processed_by VARCHAR(255),
    -- Impact analysis
    affected_documents_count INTEGER DEFAULT 0,
    estimated_compliance_impact TEXT,
    recommended_actions TEXT[],
    metadata JSONB, -- Additional regulatory data
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table: compliance_reports
-- Stores generated compliance reports
CREATE TABLE compliance_reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    report_id VARCHAR(255) UNIQUE NOT NULL, -- External report identifier
    report_type report_type_enum NOT NULL,
    generated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    compliance_rate DECIMAL(5,2) NOT NULL, -- Percentage (0.00 to 100.00)
    -- Report content
    executive_summary TEXT,
    recommendations TEXT[],
    report_data JSONB, -- Full report data structure
    -- File information
    file_path VARCHAR(1000),
    file_size BIGINT,
    file_format VARCHAR(50) DEFAULT 'PDF',
    -- Distribution
    recipients TEXT[],
    distributed_at TIMESTAMP WITH TIME ZONE,
    distributed_by VARCHAR(255),
    -- Metrics
    total_documents INTEGER DEFAULT 0,
    compliant_documents INTEGER DEFAULT 0,
    violations_count INTEGER DEFAULT 0,
    critical_violations INTEGER DEFAULT 0,
    high_violations INTEGER DEFAULT 0,
    medium_violations INTEGER DEFAULT 0,
    low_violations INTEGER DEFAULT 0,
    -- Period covered
    period_start DATE,
    period_end DATE,
    metadata JSONB, -- Additional report data
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table: compliance_report_violations
-- Links violations to reports (many-to-many relationship)
CREATE TABLE compliance_report_violations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    report_id UUID NOT NULL REFERENCES compliance_reports(id) ON DELETE CASCADE,
    violation_id UUID NOT NULL REFERENCES compliance_violations(id) ON DELETE CASCADE,
    included_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(report_id, violation_id)
);

-- =====================================================
-- WORKFLOW AND AUDIT TABLES
-- =====================================================

-- Table: workflow_executions
-- Tracks DBOS workflow executions
CREATE TABLE workflow_executions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    workflow_id VARCHAR(255) UNIQUE NOT NULL, -- DBOS workflow ID
    workflow_name VARCHAR(255) NOT NULL,
    workflow_type VARCHAR(100) NOT NULL, -- compliance_check, kyc_process, report_generation
    status VARCHAR(50) NOT NULL, -- running, completed, failed, cancelled
    started_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE,
    duration_ms BIGINT, -- Execution duration in milliseconds
    -- Input/Output
    input_data JSONB,
    output_data JSONB,
    error_message TEXT,
    -- Related entities
    document_id UUID REFERENCES compliance_documents(id),
    kyc_profile_id UUID REFERENCES kyc_profiles(id),
    report_id UUID REFERENCES compliance_reports(id),
    -- Execution context
    executor_id VARCHAR(255),
    queue_name VARCHAR(255),
    retry_count INTEGER DEFAULT 0,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table: workflow_events
-- Stores workflow events and progress tracking
CREATE TABLE workflow_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    workflow_execution_id UUID NOT NULL REFERENCES workflow_executions(id) ON DELETE CASCADE,
    event_name VARCHAR(255) NOT NULL,
    event_data JSONB,
    occurred_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    step_name VARCHAR(255),
    step_order INTEGER,
    metadata JSONB
);

-- Table: audit_logs
-- Comprehensive audit trail for all system activities
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    -- Event identification
    event_type VARCHAR(100) NOT NULL, -- document_upload, violation_detected, kyc_approved, etc.
    event_category VARCHAR(50) NOT NULL, -- compliance, kyc, reporting, system
    event_description TEXT NOT NULL,
    -- Actor information
    user_id VARCHAR(255),
    user_name VARCHAR(255),
    user_role VARCHAR(100),
    ip_address INET,
    user_agent TEXT,
    -- Target information
    target_type VARCHAR(100), -- document, kyc_profile, report, rule, etc.
    target_id VARCHAR(255),
    target_name VARCHAR(500),
    -- Change tracking
    old_values JSONB,
    new_values JSONB,
    changes JSONB, -- Specific fields that changed
    -- Context
    workflow_id VARCHAR(255),
    session_id VARCHAR(255),
    request_id VARCHAR(255),
    -- Risk and compliance
    risk_level severity_enum,
    compliance_impact BOOLEAN DEFAULT false,
    requires_review BOOLEAN DEFAULT false,
    -- Metadata
    metadata JSONB,
    occurred_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- SYSTEM CONFIGURATION AND MONITORING
-- =====================================================

-- Table: system_configuration
-- Stores system-wide configuration settings
CREATE TABLE system_configuration (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    config_key VARCHAR(255) UNIQUE NOT NULL,
    config_value TEXT NOT NULL,
    config_type VARCHAR(50) NOT NULL, -- string, number, boolean, json
    description TEXT,
    category VARCHAR(100), -- compliance, kyc, reporting, system
    is_sensitive BOOLEAN DEFAULT false, -- For encrypted values
    is_active BOOLEAN DEFAULT true,
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table: notifications
-- Stores system notifications and alerts
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    notification_type VARCHAR(100) NOT NULL, -- violation_alert, kyc_review, regulatory_update
    title VARCHAR(500) NOT NULL,
    message TEXT NOT NULL,
    severity severity_enum NOT NULL,
    -- Target information
    target_users TEXT[], -- Array of user IDs to notify
    target_roles TEXT[], -- Array of roles to notify
    -- Related entities
    document_id UUID REFERENCES compliance_documents(id),
    violation_id UUID REFERENCES compliance_violations(id),
    kyc_profile_id UUID REFERENCES kyc_profiles(id),
    regulatory_update_id UUID REFERENCES regulatory_updates(id),
    workflow_id VARCHAR(255),
    -- Delivery tracking
    sent_at TIMESTAMP WITH TIME ZONE,
    delivery_method VARCHAR(50), -- email, sms, in_app, webhook
    delivery_status VARCHAR(50) DEFAULT 'pending', -- pending, sent, delivered, failed
    delivery_attempts INTEGER DEFAULT 0,
    last_attempt_at TIMESTAMP WITH TIME ZONE,
    -- Response tracking
    read_at TIMESTAMP WITH TIME ZONE,
    acknowledged_at TIMESTAMP WITH TIME ZONE,
    acknowledged_by VARCHAR(255),
    -- Expiration
    expires_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table: performance_metrics
-- Stores system performance and compliance metrics
CREATE TABLE performance_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    metric_name VARCHAR(255) NOT NULL,
    metric_category VARCHAR(100) NOT NULL, -- compliance, kyc, workflow, system
    metric_value DECIMAL(15,4) NOT NULL,
    metric_unit VARCHAR(50), -- percentage, seconds, count, bytes
    -- Time dimension
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    period_start TIMESTAMP WITH TIME ZONE,
    period_end TIMESTAMP WITH TIME ZONE,
    aggregation_level VARCHAR(50), -- real_time, hourly, daily, weekly, monthly
    -- Context
    workflow_type VARCHAR(100),
    document_type document_type_enum,
    compliance_standard compliance_standard_enum,
    -- Additional dimensions
    dimensions JSONB, -- Additional metric dimensions
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table: compliance_standards_config
-- Configuration for different compliance standards
CREATE TABLE compliance_standards_config (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    standard compliance_standard_enum NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    -- Monitoring configuration
    monitoring_enabled BOOLEAN DEFAULT true,
    update_check_frequency VARCHAR(50) DEFAULT 'daily', -- hourly, daily, weekly
    last_update_check TIMESTAMP WITH TIME ZONE,
    -- Notification settings
    notification_enabled BOOLEAN DEFAULT true,
    critical_violation_alert BOOLEAN DEFAULT true,
    regulatory_update_alert BOOLEAN DEFAULT true,
    -- Processing settings
    auto_processing_enabled BOOLEAN DEFAULT true,
    manual_review_threshold INTEGER DEFAULT 70, -- Risk score threshold
    -- URLs and sources
    official_website TEXT,
    documentation_url TEXT,
    api_endpoint TEXT,
    rss_feed_url TEXT,
    -- Metadata
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(standard)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE OPTIMIZATION
-- =====================================================

-- Compliance Documents indexes
CREATE INDEX idx_compliance_documents_status ON compliance_documents(status);
CREATE INDEX idx_compliance_documents_type ON compliance_documents(document_type);
CREATE INDEX idx_compliance_documents_uploaded_at ON compliance_documents(uploaded_at);
CREATE INDEX idx_compliance_documents_document_id ON compliance_documents(document_id);

-- Compliance Rules indexes
CREATE INDEX idx_compliance_rules_standard ON compliance_rules(standard);
CREATE INDEX idx_compliance_rules_severity ON compliance_rules(severity);
CREATE INDEX idx_compliance_rules_active ON compliance_rules(is_active);
CREATE INDEX idx_compliance_rules_rule_id ON compliance_rules(rule_id);

-- KYC Profiles indexes
CREATE INDEX idx_kyc_profiles_status ON kyc_profiles(status);
CREATE INDEX idx_kyc_profiles_risk_score ON kyc_profiles(risk_score);
CREATE INDEX idx_kyc_profiles_customer_id ON kyc_profiles(customer_id);
CREATE INDEX idx_kyc_profiles_last_updated ON kyc_profiles(last_updated);

-- Compliance Violations indexes
CREATE INDEX idx_compliance_violations_severity ON compliance_violations(severity);
CREATE INDEX idx_compliance_violations_status ON compliance_violations(status);
CREATE INDEX idx_compliance_violations_detected_at ON compliance_violations(detected_at);
CREATE INDEX idx_compliance_violations_document_id ON compliance_violations(document_id);
CREATE INDEX idx_compliance_violations_rule_id ON compliance_violations(rule_id);

-- Regulatory Updates indexes
CREATE INDEX idx_regulatory_updates_standard ON regulatory_updates(standard);
CREATE INDEX idx_regulatory_updates_impact ON regulatory_updates(impact);
CREATE INDEX idx_regulatory_updates_effective_date ON regulatory_updates(effective_date);
CREATE INDEX idx_regulatory_updates_processed ON regulatory_updates(processed);

-- Compliance Reports indexes
CREATE INDEX idx_compliance_reports_type ON compliance_reports(report_type);
CREATE INDEX idx_compliance_reports_generated_at ON compliance_reports(generated_at);
CREATE INDEX idx_compliance_reports_period ON compliance_reports(period_start, period_end);

-- Workflow Executions indexes
CREATE INDEX idx_workflow_executions_status ON workflow_executions(status);
CREATE INDEX idx_workflow_executions_type ON workflow_executions(workflow_type);
CREATE INDEX idx_workflow_executions_started_at ON workflow_executions(started_at);
CREATE INDEX idx_workflow_executions_workflow_id ON workflow_executions(workflow_id);

-- Audit Logs indexes
CREATE INDEX idx_audit_logs_event_type ON audit_logs(event_type);
CREATE INDEX idx_audit_logs_event_category ON audit_logs(event_category);
CREATE INDEX idx_audit_logs_occurred_at ON audit_logs(occurred_at);
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_target_type_id ON audit_logs(target_type, target_id);

-- Notifications indexes
CREATE INDEX idx_notifications_type ON notifications(notification_type);
CREATE INDEX idx_notifications_severity ON notifications(severity);
CREATE INDEX idx_notifications_delivery_status ON notifications(delivery_status);
CREATE INDEX idx_notifications_created_at ON notifications(created_at);

-- Performance Metrics indexes
CREATE INDEX idx_performance_metrics_name_category ON performance_metrics(metric_name, metric_category);
CREATE INDEX idx_performance_metrics_recorded_at ON performance_metrics(recorded_at);
CREATE INDEX idx_performance_metrics_aggregation ON performance_metrics(aggregation_level);

-- =====================================================
-- TRIGGERS FOR AUTOMATIC TIMESTAMP UPDATES
-- =====================================================

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply the trigger to all tables with updated_at columns
CREATE TRIGGER update_compliance_documents_updated_at BEFORE UPDATE ON compliance_documents FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_compliance_rules_updated_at BEFORE UPDATE ON compliance_rules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_kyc_profiles_updated_at BEFORE UPDATE ON kyc_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_compliance_violations_updated_at BEFORE UPDATE ON compliance_violations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_regulatory_updates_updated_at BEFORE UPDATE ON regulatory_updates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_compliance_reports_updated_at BEFORE UPDATE ON compliance_reports FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_workflow_executions_updated_at BEFORE UPDATE ON workflow_executions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_system_configuration_updated_at BEFORE UPDATE ON system_configuration FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_notifications_updated_at BEFORE UPDATE ON notifications FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_compliance_standards_config_updated_at BEFORE UPDATE ON compliance_standards_config FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- UTILITY FUNCTIONS
-- =====================================================

-- Function to encrypt sensitive data
CREATE OR REPLACE FUNCTION encrypt_sensitive_data(data TEXT)
RETURNS TEXT AS $$
BEGIN
    RETURN encode(pgp_sym_encrypt(data, current_setting('app.encryption_key', true)), 'base64');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to decrypt sensitive data
CREATE OR REPLACE FUNCTION decrypt_sensitive_data(encrypted_data TEXT)
RETURNS TEXT AS $$
BEGIN
    RETURN pgp_sym_decrypt(decode(encrypted_data, 'base64'), current_setting('app.encryption_key', true));
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to calculate compliance rate
CREATE OR REPLACE FUNCTION calculate_compliance_rate(
    p_start_date DATE DEFAULT NULL,
    p_end_date DATE DEFAULT NULL,
    p_document_type document_type_enum DEFAULT NULL
)
RETURNS DECIMAL(5,2) AS $$
DECLARE
    total_docs INTEGER;
    compliant_docs INTEGER;
    compliance_rate DECIMAL(5,2);
BEGIN
    -- Set default dates if not provided
    IF p_start_date IS NULL THEN
        p_start_date := CURRENT_DATE - INTERVAL '30 days';
    END IF;
    IF p_end_date IS NULL THEN
        p_end_date := CURRENT_DATE;
    END IF;

    -- Count total documents
    SELECT COUNT(*)
    INTO total_docs
    FROM compliance_documents
    WHERE uploaded_at::DATE BETWEEN p_start_date AND p_end_date
    AND (p_document_type IS NULL OR document_type = p_document_type);

    -- Count compliant documents
    SELECT COUNT(*)
    INTO compliant_docs
    FROM compliance_documents
    WHERE uploaded_at::DATE BETWEEN p_start_date AND p_end_date
    AND status = 'compliant'
    AND (p_document_type IS NULL OR document_type = p_document_type);

    -- Calculate compliance rate
    IF total_docs > 0 THEN
        compliance_rate := (compliant_docs::DECIMAL / total_docs::DECIMAL) * 100;
    ELSE
        compliance_rate := 0;
    END IF;

    RETURN compliance_rate;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- SAMPLE DATA INSERTION
-- =====================================================

-- Insert compliance standards configuration
INSERT INTO compliance_standards_config (standard, display_name, description, monitoring_enabled, notification_enabled) VALUES
('SEC', 'Securities and Exchange Commission', 'U.S. federal agency that enforces securities laws and regulates the securities industry', true, true),
('GLBA', 'Gramm-Leach-Bliley Act', 'Financial privacy rule requiring financial institutions to protect customer information', true, true),
('SOX', 'Sarbanes-Oxley Act', 'Federal law that established auditing and financial regulations for public companies', true, true),
('GDPR', 'General Data Protection Regulation', 'European Union regulation on data protection and privacy', true, true),
('CCPA', 'California Consumer Privacy Act', 'California state statute intended to enhance privacy rights for residents', true, true),
('FINRA', 'Financial Industry Regulatory Authority', 'Self-regulatory organization that regulates member brokerage firms', true, true);

-- Insert sample compliance rules (from the application)
INSERT INTO compliance_rules (rule_id, standard, rule_type, description, pattern, severity, is_active) VALUES
('SEC-001', 'SEC', 'financial_disclosure', 'Financial statements must include quarterly earnings disclosure', 'quarterly.*(earnings|revenue|income)', 'high', true),
('GLBA-001', 'GLBA', 'privacy', 'Customer financial information must be protected', '(ssn|social.security|account.number|routing.number)', 'critical', true),
('SOX-001', 'SOX', 'financial_disclosure', 'Internal controls must be documented', 'internal.control.*documentation', 'high', true),
('GDPR-001', 'GDPR', 'data_protection', 'Personal data processing must have legal basis', '(personal.data|processing|consent)', 'high', true),
('CCPA-001', 'CCPA', 'privacy', 'Consumer privacy rights must be disclosed', '(consumer.rights|privacy.policy|data.sale)', 'medium', true);

-- Insert system configuration
INSERT INTO system_configuration (config_key, config_value, config_type, description, category) VALUES
('compliance.auto_scan_enabled', 'true', 'boolean', 'Enable automatic compliance scanning for uploaded documents', 'compliance'),
('kyc.risk_threshold_high', '70', 'number', 'Risk score threshold for high-risk KYC profiles', 'kyc'),
('kyc.risk_threshold_critical', '85', 'number', 'Risk score threshold for critical-risk KYC profiles', 'kyc'),
('reporting.auto_generation_enabled', 'true', 'boolean', 'Enable automatic report generation', 'reporting'),
('notifications.email_enabled', 'true', 'boolean', 'Enable email notifications', 'system'),
('workflow.max_retry_attempts', '3', 'number', 'Maximum number of retry attempts for failed workflows', 'system'),
('compliance.violation_retention_days', '2555', 'number', 'Number of days to retain violation records (7 years)', 'compliance'),
('audit.log_retention_days', '2555', 'number', 'Number of days to retain audit logs (7 years)', 'system');

-- =====================================================
-- VIEWS FOR COMMON QUERIES
-- =====================================================

-- View: compliance_dashboard_summary
-- Provides summary data for the compliance dashboard
CREATE VIEW compliance_dashboard_summary AS
SELECT
    COUNT(CASE WHEN cd.status = 'compliant' THEN 1 END) as compliant_documents,
    COUNT(CASE WHEN cd.status = 'non_compliant' THEN 1 END) as non_compliant_documents,
    COUNT(CASE WHEN cd.status = 'requires_review' THEN 1 END) as documents_requiring_review,
    COUNT(CASE WHEN cd.status = 'processing' THEN 1 END) as documents_processing,
    COUNT(cv.id) as total_violations,
    COUNT(CASE WHEN cv.severity = 'critical' THEN 1 END) as critical_violations,
    COUNT(CASE WHEN cv.severity = 'high' THEN 1 END) as high_violations,
    COUNT(CASE WHEN cv.severity = 'medium' THEN 1 END) as medium_violations,
    COUNT(CASE WHEN cv.severity = 'low' THEN 1 END) as low_violations,
    ROUND(
        CASE
            WHEN COUNT(cd.id) > 0 THEN
                (COUNT(CASE WHEN cd.status = 'compliant' THEN 1 END)::DECIMAL / COUNT(cd.id)::DECIMAL) * 100
            ELSE 0
        END, 2
    ) as compliance_rate
FROM compliance_documents cd
LEFT JOIN compliance_violations cv ON cd.id = cv.document_id AND cv.status = 'open'
WHERE cd.uploaded_at >= CURRENT_DATE - INTERVAL '30 days';

-- View: kyc_dashboard_summary
-- Provides summary data for the KYC dashboard
CREATE VIEW kyc_dashboard_summary AS
SELECT
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_profiles,
    COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_profiles,
    COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_profiles,
    COUNT(CASE WHEN status = 'under_review' THEN 1 END) as profiles_under_review,
    AVG(risk_score) as average_risk_score,
    COUNT(CASE WHEN risk_score >= 70 THEN 1 END) as high_risk_profiles,
    COUNT(CASE WHEN sanctions_match = true THEN 1 END) as sanctions_matches
FROM kyc_profiles
WHERE last_updated >= CURRENT_DATE - INTERVAL '30 days';

-- =====================================================
-- COMMENTS AND DOCUMENTATION
-- =====================================================

COMMENT ON DATABASE dbos_kyc_demo IS 'Database for DBOS Compliance Command Center - stores all compliance, KYC, and regulatory data';

COMMENT ON TABLE compliance_documents IS 'Stores documents uploaded for compliance checking';
COMMENT ON TABLE compliance_rules IS 'Stores compliance rules used for document scanning and violation detection';
COMMENT ON TABLE kyc_profiles IS 'Stores Know Your Customer profiles with encrypted personal information';
COMMENT ON TABLE compliance_violations IS 'Stores violations found during compliance checks';
COMMENT ON TABLE regulatory_updates IS 'Stores regulatory updates and changes from various standards';
COMMENT ON TABLE compliance_reports IS 'Stores generated compliance reports';
COMMENT ON TABLE workflow_executions IS 'Tracks DBOS workflow executions and their status';
COMMENT ON TABLE audit_logs IS 'Comprehensive audit trail for all system activities';
COMMENT ON TABLE notifications IS 'Stores system notifications and alerts';
COMMENT ON TABLE performance_metrics IS 'Stores system performance and compliance metrics';

-- Grant permissions (adjust as needed for your environment)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO compliance_app_user;
-- GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO compliance_app_user;
-- GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO compliance_app_user;
