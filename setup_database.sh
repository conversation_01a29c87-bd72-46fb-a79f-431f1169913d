#!/bin/bash

# Database Setup Script for Compliance Command Center DBOS
# This script sets up the PostgreSQL database schema

set -e  # Exit on any error

# Configuration
DB_NAME="${DB_NAME:-dbos_kyc_demo}"
DB_USER="${DB_USER:-dbosadmin}"
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
SCHEMA_FILE="database_schema.sql"
MIGRATION_FILE="migrations/001_initial_schema.sql"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if PostgreSQL is running
check_postgres() {
    log_info "Checking PostgreSQL connection..."
    if ! pg_isready -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" >/dev/null 2>&1; then
        log_error "Cannot connect to PostgreSQL at $DB_HOST:$DB_PORT"
        log_error "Please ensure PostgreSQL is running and accessible"
        exit 1
    fi
    log_success "PostgreSQL is running and accessible"
}

# Check if database exists
check_database() {
    log_info "Checking if database '$DB_NAME' exists..."
    if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -lqt | cut -d \| -f 1 | grep -qw "$DB_NAME"; then
        log_warning "Database '$DB_NAME' already exists"
        read -p "Do you want to continue and potentially overwrite existing data? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "Setup cancelled by user"
            exit 0
        fi
    else
        log_info "Database '$DB_NAME' does not exist, will create it"
    fi
}

# Create database if it doesn't exist
create_database() {
    log_info "Creating database '$DB_NAME'..."
    if ! psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -lqt | cut -d \| -f 1 | grep -qw "$DB_NAME"; then
        createdb -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" "$DB_NAME"
        log_success "Database '$DB_NAME' created successfully"
    else
        log_info "Database '$DB_NAME' already exists, skipping creation"
    fi
}

# Check if schema file exists
check_schema_file() {
    if [[ ! -f "$SCHEMA_FILE" ]]; then
        log_error "Schema file '$SCHEMA_FILE' not found"
        log_error "Please ensure you're running this script from the project root directory"
        exit 1
    fi
    log_success "Schema file found: $SCHEMA_FILE"
}

# Grant permissions to user
grant_permissions() {
    log_info "Granting permissions to user '$DB_USER'..."

    # Grant permissions on public schema
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        GRANT CREATE ON SCHEMA public TO $DB_USER;
        GRANT USAGE ON SCHEMA public TO $DB_USER;
        ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO $DB_USER;
        ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO $DB_USER;
        ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO $DB_USER;
    " 2>/dev/null || {
        log_warning "Could not grant permissions as regular user, trying as superuser..."

        # Try with postgres superuser if available
        if command -v sudo >/dev/null 2>&1; then
            sudo -u postgres psql -d "$DB_NAME" -c "
                GRANT CREATE ON SCHEMA public TO $DB_USER;
                GRANT USAGE ON SCHEMA public TO $DB_USER;
                ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO $DB_USER;
                ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO $DB_USER;
                ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO $DB_USER;
            " || log_warning "Could not grant permissions automatically"
        else
            log_warning "Please run the following as a PostgreSQL superuser:"
            echo "  GRANT CREATE ON SCHEMA public TO $DB_USER;"
            echo "  GRANT USAGE ON SCHEMA public TO $DB_USER;"
            read -p "Press Enter after granting permissions, or Ctrl+C to exit..."
        fi
    }
}

# Apply database schema
apply_schema() {
    log_info "Applying database schema..."

    # Set encryption key for the session (you should use a proper key management system in production)
    export PGPASSWORD="${PGPASSWORD:-}"

    # Apply the schema
    if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f "$SCHEMA_FILE" -v ON_ERROR_STOP=1; then
        log_success "Database schema applied successfully"
    else
        log_error "Failed to apply database schema"
        log_error "This might be due to permission issues. Try running with a superuser account."
        exit 1
    fi
}

# Verify installation
verify_installation() {
    log_info "Verifying installation..."
    
    # Check if tables were created
    TABLE_COUNT=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_type = 'BASE TABLE';")
    
    if [[ $TABLE_COUNT -gt 10 ]]; then
        log_success "Installation verified: $TABLE_COUNT tables created"
    else
        log_error "Installation verification failed: only $TABLE_COUNT tables found"
        exit 1
    fi
    
    # Check if sample data was inserted
    RULES_COUNT=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM compliance_rules;")
    log_info "Sample compliance rules inserted: $RULES_COUNT"
    
    CONFIG_COUNT=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM system_configuration;")
    log_info "System configuration entries: $CONFIG_COUNT"
}

# Display connection info
display_connection_info() {
    log_success "Database setup completed successfully!"
    echo
    echo "Connection Information:"
    echo "  Database: $DB_NAME"
    echo "  Host: $DB_HOST"
    echo "  Port: $DB_PORT"
    echo "  User: $DB_USER"
    echo
    echo "Database URL for DBOS:"
    echo "  postgresql://$DB_USER@$DB_HOST:$DB_PORT/$DB_NAME"
    echo
    echo "Set the following environment variable:"
    echo "  export DBOS_DATABASE_URL=\"postgresql://$DB_USER@$DB_HOST:$DB_PORT/$DB_NAME\""
    echo
    log_info "You can now start the Compliance Command Center application"
}

# Main execution
main() {
    echo "=============================================="
    echo "  Compliance Command Center DBOS Setup"
    echo "=============================================="
    echo
    
    check_postgres
    check_database
    create_database
    grant_permissions
    check_schema_file
    apply_schema
    verify_installation
    display_connection_info
}

# Help function
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo
    echo "Setup PostgreSQL database for Compliance Command Center DBOS"
    echo
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  -n, --name     Database name (default: compliance_command_center)"
    echo "  -u, --user     Database user (default: postgres)"
    echo "  -H, --host     Database host (default: localhost)"
    echo "  -p, --port     Database port (default: 5432)"
    echo
    echo "Environment Variables:"
    echo "  DB_NAME        Database name"
    echo "  DB_USER        Database user"
    echo "  DB_HOST        Database host"
    echo "  DB_PORT        Database port"
    echo "  PGPASSWORD     Database password"
    echo
    echo "Examples:"
    echo "  $0                                    # Use defaults"
    echo "  $0 -n mydb -u myuser -H myhost       # Custom settings"
    echo "  DB_NAME=testdb $0                    # Using environment variables"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -n|--name)
            DB_NAME="$2"
            shift 2
            ;;
        -u|--user)
            DB_USER="$2"
            shift 2
            ;;
        -H|--host)
            DB_HOST="$2"
            shift 2
            ;;
        -p|--port)
            DB_PORT="$2"
            shift 2
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Run main function
main
