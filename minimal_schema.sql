-- Minimal Database Schema for Compliance Command Center DBOS
-- This is a simplified version for quick testing and development

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create <PERSON><PERSON><PERSON> types
CREATE TYPE document_type_enum AS ENUM ('contract', 'policy', 'procedure', 'financial_report');
CREATE TYPE document_status_enum AS ENUM ('pending', 'processing', 'compliant', 'non_compliant', 'requires_review');
CREATE TYPE compliance_standard_enum AS ENUM ('SEC', 'GLBA', 'SOX', 'GDPR', 'CCPA', 'FINRA');
CREATE TYPE rule_type_enum AS ENUM ('data_protection', 'financial_disclosure', 'privacy', 'security');
CREATE TYPE severity_enum AS ENUM ('low', 'medium', 'high', 'critical');
CREATE TYPE kyc_status_enum AS ENUM ('pending', 'approved', 'rejected', 'under_review');
CREATE TYPE report_type_enum AS ENUM ('monthly', 'quarterly', 'annual', 'incident');
CREATE TYPE impact_level_enum AS ENUM ('low', 'medium', 'high');

-- Core tables only

-- Table: compliance_documents
CREATE TABLE compliance_documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    document_id VARCHAR(255) UNIQUE NOT NULL,
    content TEXT NOT NULL,
    document_type document_type_enum NOT NULL,
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    status document_status_enum DEFAULT 'pending',
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table: compliance_rules
CREATE TABLE compliance_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    rule_id VARCHAR(100) UNIQUE NOT NULL,
    standard compliance_standard_enum NOT NULL,
    rule_type rule_type_enum NOT NULL,
    description TEXT NOT NULL,
    pattern TEXT NOT NULL,
    severity severity_enum NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table: kyc_profiles
CREATE TABLE kyc_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    date_of_birth DATE NOT NULL,
    ssn VARCHAR(20) NOT NULL,
    address TEXT NOT NULL,
    risk_score INTEGER CHECK (risk_score >= 0 AND risk_score <= 100),
    status kyc_status_enum DEFAULT 'pending',
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table: compliance_violations
CREATE TABLE compliance_violations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    document_id UUID NOT NULL REFERENCES compliance_documents(id) ON DELETE CASCADE,
    rule_id UUID NOT NULL REFERENCES compliance_rules(id) ON DELETE CASCADE,
    violation_type VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    severity severity_enum NOT NULL,
    recommended_action TEXT NOT NULL,
    detected_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table: regulatory_updates
CREATE TABLE regulatory_updates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    update_id VARCHAR(255) UNIQUE NOT NULL,
    standard compliance_standard_enum NOT NULL,
    title VARCHAR(500) NOT NULL,
    description TEXT NOT NULL,
    effective_date DATE NOT NULL,
    impact impact_level_enum NOT NULL,
    action_required BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table: compliance_reports
CREATE TABLE compliance_reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    report_id VARCHAR(255) UNIQUE NOT NULL,
    report_type report_type_enum NOT NULL,
    generated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    compliance_rate DECIMAL(5,2) NOT NULL,
    recommendations TEXT[],
    report_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Basic indexes
CREATE INDEX idx_compliance_documents_status ON compliance_documents(status);
CREATE INDEX idx_compliance_documents_type ON compliance_documents(document_type);
CREATE INDEX idx_compliance_violations_severity ON compliance_violations(severity);
CREATE INDEX idx_kyc_profiles_status ON kyc_profiles(status);

-- Insert sample compliance rules
INSERT INTO compliance_rules (rule_id, standard, rule_type, description, pattern, severity, is_active) VALUES
('SEC-001', 'SEC', 'financial_disclosure', 'Financial statements must include quarterly earnings disclosure', 'quarterly.*(earnings|revenue|income)', 'high', true),
('GLBA-001', 'GLBA', 'privacy', 'Customer financial information must be protected', '(ssn|social.security|account.number|routing.number)', 'critical', true),
('SOX-001', 'SOX', 'financial_disclosure', 'Internal controls must be documented', 'internal.control.*documentation', 'high', true),
('GDPR-001', 'GDPR', 'data_protection', 'Personal data processing must have legal basis', '(personal.data|processing|consent)', 'high', true),
('CCPA-001', 'CCPA', 'privacy', 'Consumer privacy rights must be disclosed', '(consumer.rights|privacy.policy|data.sale)', 'medium', true);

-- Sample regulatory updates
INSERT INTO regulatory_updates (update_id, standard, title, description, effective_date, impact, action_required) VALUES
('SEC-2024-001', 'SEC', 'Updated Cybersecurity Disclosure Requirements', 'New requirements for cybersecurity incident reporting within 4 business days', '2024-12-01', 'high', true),
('GLBA-2024-002', 'GLBA', 'Enhanced Privacy Notice Requirements', 'Updated privacy notice requirements for financial institutions', '2024-11-15', 'medium', true);

-- Create a simple view for dashboard
CREATE VIEW compliance_summary AS
SELECT 
    COUNT(*) as total_documents,
    COUNT(CASE WHEN status = 'compliant' THEN 1 END) as compliant_documents,
    COUNT(CASE WHEN status = 'non_compliant' THEN 1 END) as non_compliant_documents,
    ROUND(
        CASE 
            WHEN COUNT(*) > 0 THEN 
                (COUNT(CASE WHEN status = 'compliant' THEN 1 END)::DECIMAL / COUNT(*)::DECIMAL) * 100
            ELSE 0 
        END, 2
    ) as compliance_rate
FROM compliance_documents
WHERE uploaded_at >= CURRENT_DATE - INTERVAL '30 days';

-- Verification
SELECT 'Schema created successfully!' as status;
SELECT COUNT(*) as compliance_rules_count FROM compliance_rules;
SELECT COUNT(*) as regulatory_updates_count FROM regulatory_updates;
