#!/usr/bin/env node

// Quick test to submit KYC and check status
import fetch from 'node-fetch';

const API_BASE_URL = 'http://localhost:3000';

async function quickTest() {
  console.log('🧪 Quick KYC test...\n');

  try {
    // Submit a KYC profile
    console.log('Submitting KYC profile...');
    const kycProfile = {
      customerId: `CUST-${Date.now()}`,
      personalInfo: {
        name: 'Quick Test Customer',
        dateOfBirth: '1990-01-01',
        ssn: '***********',
        address: '123 Test Street, Test City, TS 12345'
      },
      riskScore: 0,
      status: 'pending',
      lastUpdated: new Date()
    };

    const submitResponse = await fetch(`${API_BASE_URL}/api/kyc/customer`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(kycProfile),
    });

    if (!submitResponse.ok) {
      throw new Error(`Failed to submit KYC: ${submitResponse.statusText}`);
    }

    const submitResult = await submitResponse.json();
    console.log('✅ KYC submitted!');
    console.log(`   Workflow ID: ${submitResult.workflowId}\n`);

    // Poll a few times to see the status changes
    const workflowId = submitResult.workflowId;
    
    for (let i = 1; i <= 10; i++) {
      console.log(`Poll ${i}:`);
      
      try {
        const statusResponse = await fetch(`${API_BASE_URL}/api/workflow/${workflowId}/status`);
        
        if (statusResponse.ok) {
          const statusResult = await statusResponse.json();
          console.log(`   Status: ${statusResult.status}`);
          
          if (statusResult.events && statusResult.events.kyc_status) {
            console.log(`   Step: ${statusResult.events.kyc_status}`);
          }
          
          if (statusResult.status === 'completed' || statusResult.status === 'failed') {
            console.log(`\n🎉 Workflow ${statusResult.status}! Stopping test.`);
            break;
          }
        } else {
          console.log(`   Error: ${statusResponse.status}`);
        }
      } catch (error) {
        console.log(`   Error: ${error.message}`);
      }
      
      // Wait 3 seconds
      await new Promise(resolve => setTimeout(resolve, 3000));
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

quickTest();
