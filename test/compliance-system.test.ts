import { DocumentProcessing } from '../src/workflows/document-processing';
import { ComplianceDatabase } from '../src/database/compliance-database';
import { DBOS } from '@dbos-inc/dbos-sdk';

// Mock DBOS SDK
jest.mock('@dbos-inc/dbos-sdk', () => {
  return {
    DBOS: {
      step: jest.fn().mockImplementation(() => (target: any, propertyKey: string) => {}),
      workflow: jest.fn().mockImplementation(() => (target: any, propertyKey: string) => {}),
      scheduled: jest.fn().mockImplementation(() => (target: any, propertyKey: string) => {}),
      transaction: jest.fn().mockImplementation(() => (target: any, propertyKey: string) => {}),
      sleep: jest.fn().mockResolvedValue(undefined),
      setEvent: jest.fn().mockResolvedValue(undefined),
      getEvent: jest.fn().mockResolvedValue(undefined),
      setConfig: jest.fn(),
      launch: jest.fn().mockResolvedValue(undefined),
      pgClient: {
        query: jest.fn().mockResolvedValue({ rows: [] })
      },
      logger: {
        info: jest.fn(),
        warn: jest.fn(),
        error: jest.fn()
      }
    },
    WorkflowQueue: jest.fn().mockImplementation(() => ({
      name: 'mock-queue'
    })),
    ConfiguredInstance: jest.fn()
  };
});

describe('DocumentProcessing', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock database methods
    jest.spyOn(ComplianceDatabase, 'saveDocument').mockResolvedValue('mock-id');
    jest.spyOn(ComplianceDatabase, 'getActiveComplianceRules').mockResolvedValue([]);
    jest.spyOn(ComplianceDatabase, 'saveViolations').mockResolvedValue(undefined);
  });

  describe('validateDocument', () => {
    it('should return true for valid documents', async () => {
      const document = {
        id: 'doc-123',
        content: 'This is a valid document with sufficient content to pass validation checks. It contains more than 100 characters to ensure it meets the minimum length requirement.',
        documentType: 'contract' as const,
        uploadedAt: new Date(),
        status: 'pending' as const
      };

      const result = await DocumentProcessing.validateDocument(document);
      expect(result).toBe(true);
      expect(DBOS.logger.info).toHaveBeenCalledWith(`Validating document ${document.id}`);
      expect(DBOS.logger.info).toHaveBeenCalledWith(`Document ${document.id} validation completed`);
    });

    it('should return false for documents with insufficient content', async () => {
      const document = {
        id: 'doc-456',
        content: 'Too short',
        documentType: 'contract' as const,
        uploadedAt: new Date(),
        status: 'pending' as const
      };

      const result = await DocumentProcessing.validateDocument(document);
      expect(result).toBe(false);
      expect(DBOS.logger.warn).toHaveBeenCalledWith(`Document ${document.id} failed validation - insufficient content`);
    });
  });
});
