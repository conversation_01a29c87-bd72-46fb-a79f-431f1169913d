import { ComplianceSystem } from '../src/workflows/compliance-system';
import { DocumentProcessing } from '../src/workflows/document-processing';
import { KYCProcessing } from '../src/workflows/kyc-processing';
import { ReportGeneration } from '../src/workflows/report-generation';
import { ComplianceDatabase } from '../src/database/compliance-database';
import { DBOS } from '@dbos-inc/dbos-sdk';

// Mock DBOS SDK
jest.mock('@dbos-inc/dbos-sdk', () => {
  return {
    DBOS: {
      step: jest.fn().mockImplementation(() => (target: any, propertyKey: string) => {}),
      workflow: jest.fn().mockImplementation(() => (target: any, propertyKey: string) => {}),
      scheduled: jest.fn().mockImplementation(() => (target: any, propertyKey: string) => {}),
      transaction: jest.fn().mockImplementation(() => (target: any, propertyKey: string) => {}),
      sleep: jest.fn().mockResolvedValue(undefined),
      setEvent: jest.fn().mockResolvedValue(undefined),
      getEvent: jest.fn().mockResolvedValue(undefined),
      setConfig: jest.fn(),
      launch: jest.fn().mockResolvedValue(undefined),
      startWorkflow: jest.fn().mockReturnValue({
        processComplianceDocument: jest.fn().mockResolvedValue({ workflowID: 'mock-id' }),
        processKYCCustomer: jest.fn().mockResolvedValue({ workflowID: 'mock-id' }),
        generateComplianceReport: jest.fn().mockResolvedValue({ workflowID: 'mock-id' })
      }),
      retrieveWorkflow: jest.fn().mockResolvedValue({
        getStatus: jest.fn().mockResolvedValue({ status: 'completed', workflowName: 'mockWorkflow' }),
        getResult: jest.fn().mockResolvedValue({ status: 'success' })
      }),
      pgClient: {
        query: jest.fn().mockResolvedValue({ rows: [] })
      },
      logger: {
        info: jest.fn(),
        warn: jest.fn(),
        error: jest.fn()
      }
    },
    WorkflowQueue: jest.fn().mockImplementation(() => ({
      name: 'mock-queue'
    })),
    ConfiguredInstance: jest.fn()
  };
});

describe('Workflow Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock database methods
    jest.spyOn(ComplianceDatabase, 'saveDocument').mockResolvedValue('mock-id');
    jest.spyOn(ComplianceDatabase, 'saveKYCProfile').mockResolvedValue('mock-id');
    jest.spyOn(ComplianceDatabase, 'saveComplianceReport').mockResolvedValue('mock-id');
    jest.spyOn(ComplianceDatabase, 'getComplianceMetrics').mockResolvedValue({
      totalDocuments: 1000,
      compliantDocuments: 850,
      violationsCount: 150,
      complianceRate: 85
    });
    jest.spyOn(ComplianceDatabase, 'getRegulatoryUpdates').mockResolvedValue([]);

    // Mock the individual steps for workflow testing
    jest.spyOn(DocumentProcessing, 'validateDocument').mockResolvedValue(true);
    jest.spyOn(DocumentProcessing, 'scanForViolations').mockResolvedValue([]);
    jest.spyOn(DocumentProcessing, 'notifyComplianceTeam').mockResolvedValue(undefined);

    jest.spyOn(KYCProcessing, 'verifyIdentity').mockResolvedValue({ verified: true, confidence: 0.9 });
    jest.spyOn(KYCProcessing, 'performRiskAssessment').mockResolvedValue(30);
    jest.spyOn(KYCProcessing, 'checkSanctionsList').mockResolvedValue({ isListed: false });

    jest.spyOn(ReportGeneration, 'generateComplianceMetrics').mockResolvedValue({
      totalDocuments: 1000,
      compliantDocuments: 850,
      violationsCount: 150,
      complianceRate: 85
    });
    jest.spyOn(ReportGeneration, 'fetchRegulatoryUpdates').mockResolvedValue([]);
    jest.spyOn(ReportGeneration, 'analyzeRegulatoryImpact').mockResolvedValue([]);
    jest.spyOn(ReportGeneration, 'formatComplianceReport').mockResolvedValue({
      id: 'report-123',
      reportType: 'monthly',
      generatedAt: new Date(),
      compliance_rate: 85,
      violations: [],
      recommendations: []
    });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('processComplianceDocument workflow', () => {
    it('should process compliant documents successfully', async () => {
      const document = {
        id: 'doc-123',
        content: 'This is a valid document with sufficient content.',
        documentType: 'contract' as const,
        uploadedAt: new Date(),
        status: 'pending' as const
      };

      const result = await ComplianceSystem.processComplianceDocument(document);
      
      expect(result.status).toBe('compliant');
      expect(result.violations).toEqual([]);
      expect(DBOS.setEvent).toHaveBeenCalledWith('processing_status', 'started');
      expect(DBOS.setEvent).toHaveBeenCalledWith('processing_status', 'validation_passed');
      expect(DBOS.setEvent).toHaveBeenCalledWith('violations_found', 0);
      expect(DBOS.setEvent).toHaveBeenCalledWith('processing_status', 'completed');
    });

    it('should handle non-compliant documents correctly', async () => {
      // Mock violations for this test
      const mockViolations = [
        {
          documentId: 'doc-456',
          ruleId: 'GLBA-001',
          violationType: 'privacy',
          description: 'Potential GLBA violation',
          severity: 'critical' as const,
          recommendedAction: 'Immediate remediation required',
          detectedAt: new Date()
        }
      ];
      jest.spyOn(DocumentProcessing, 'scanForViolations').mockResolvedValue(mockViolations);

      const document = {
        id: 'doc-456',
        content: 'This document contains sensitive information.',
        documentType: 'contract' as const,
        uploadedAt: new Date(),
        status: 'pending' as const
      };

      const result = await ComplianceSystem.processComplianceDocument(document);

      expect(result.status).toBe('non_compliant');
      expect(result.violations).toEqual(mockViolations);
      expect(DBOS.setEvent).toHaveBeenCalledWith('processing_status', 'violations_reported');
      expect(DocumentProcessing.notifyComplianceTeam).toHaveBeenCalledWith(mockViolations);
    });
  });

  describe('processKYCCustomer workflow', () => {
    it('should approve low-risk customers', async () => {
      const profile = {
        customerId: 'cust-123',
        personalInfo: {
          name: 'John Johnson',
          dateOfBirth: '1980-01-01',
          ssn: '***********',
          address: '123 Main Street, Anytown, USA 12345'
        },
        riskScore: 0,
        status: 'pending' as const,
        lastUpdated: new Date()
      };

      const result = await ComplianceSystem.processKYCCustomer(profile);
      
      expect(result.status).toBe('approved');
      expect(result.riskScore).toBe(30);
      expect(DBOS.setEvent).toHaveBeenCalledWith('kyc_status', 'completed');
      expect(DBOS.setEvent).toHaveBeenCalledWith('final_status', 'approved');
    });

    it('should flag high-risk customers for review', async () => {
      jest.spyOn(KYCProcessing, 'performRiskAssessment').mockResolvedValue(75);

      const profile = {
        customerId: 'cust-456',
        personalInfo: {
          name: 'Jane Smith',
          dateOfBirth: '2000-01-01',
          ssn: '***********',
          address: '456 High Risk Ave, Risktown, USA 90001'
        },
        riskScore: 0,
        status: 'pending' as const,
        lastUpdated: new Date()
      };

      const result = await ComplianceSystem.processKYCCustomer(profile);

      expect(result.status).toBe('under_review');
      expect(result.riskScore).toBe(75);
      expect(result.reasons).toContain('High risk score requires manual review');
    });
  });
});
