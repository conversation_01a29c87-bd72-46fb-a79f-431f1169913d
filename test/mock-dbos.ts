// Common mock for DBOS SDK to be used across all test files
export const mockDBOS = {
  DBOS: {
    step: jest.fn().mockImplementation(() => (target: any, propertyKey: string) => {}),
    workflow: jest.fn().mockImplementation(() => (target: any, propertyKey: string) => {}),
    scheduled: jest.fn().mockImplementation(() => (target: any, propertyKey: string) => {}),
    sleep: jest.fn().mockResolvedValue(undefined),
    setEvent: jest.fn().mockResolvedValue(undefined),
    getEvent: jest.fn().mockResolvedValue(undefined),
    setConfig: jest.fn(),
    launch: jest.fn().mockResolvedValue(undefined),
    startWorkflow: jest.fn().mockReturnValue({
      processComplianceDocument: jest.fn().mockResolvedValue({ workflowID: 'mock-id' }),
      processKYCCustomer: jest.fn().mockResolvedValue({ workflowID: 'mock-id' }),
      generateComplianceReport: jest.fn().mockResolvedValue({ workflowID: 'mock-id' })
    }),
    retrieveWorkflow: jest.fn().mockResolvedValue({
      getStatus: jest.fn().mockResolvedValue({ status: 'completed', workflowName: 'mockWorkflow' }),
      getResult: jest.fn().mockResolvedValue({ status: 'success' })
    }),
    logger: {
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn()
    }
  },
  WorkflowQueue: jest.fn().mockImplementation(() => ({
    name: 'mock-queue'
  })),
  ConfiguredInstance: jest.fn()
};
