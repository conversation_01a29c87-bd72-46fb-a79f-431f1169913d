import { DocumentProcessing } from '../src/workflows/document-processing';
import { ComplianceDatabase } from '../src/database/compliance-database';
import { ComplianceRule, ComplianceViolation } from '../src/types';
import { DBOS } from '@dbos-inc/dbos-sdk';

// Mock DBOS SDK
jest.mock('@dbos-inc/dbos-sdk', () => {
  return {
    DBOS: {
      step: jest.fn().mockImplementation(() => (target: any, propertyKey: string) => {}),
      workflow: jest.fn().mockImplementation(() => (target: any, propertyKey: string) => {}),
      scheduled: jest.fn().mockImplementation(() => (target: any, propertyKey: string) => {}),
      transaction: jest.fn().mockImplementation(() => (target: any, propertyKey: string) => {}),
      sleep: jest.fn().mockResolvedValue(undefined),
      setEvent: jest.fn().mockResolvedValue(undefined),
      setConfig: jest.fn(),
      launch: jest.fn().mockResolvedValue(undefined),
      pgClient: {
        query: jest.fn().mockResolvedValue({ rows: [] })
      },
      logger: {
        info: jest.fn(),
        warn: jest.fn(),
        error: jest.fn()
      }
    },
    WorkflowQueue: jest.fn().mockImplementation(() => ({
      name: 'mock-queue'
    })),
    ConfiguredInstance: jest.fn()
  };
});

describe('Document Scanning', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock database methods
    jest.spyOn(ComplianceDatabase, 'getActiveComplianceRules').mockResolvedValue([
      {
        id: 'GLBA-001',
        standard: 'GLBA',
        ruleType: 'privacy',
        description: 'Customer financial information must be protected',
        pattern: 'social security|account number|financial information',
        severity: 'critical'
      }
    ]);
    jest.spyOn(ComplianceDatabase, 'saveViolations').mockResolvedValue(undefined);

    // Mock the analyzeViolationContext method to control test behavior
    jest.spyOn(DocumentProcessing, 'analyzeViolationContext').mockImplementation(
      async (content: string, rule: ComplianceRule, matches: string[]) => {
        // Return true for critical rules to simulate violations
        return rule.severity === 'critical';
      }
    );
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('scanForViolations', () => {
    it('should detect violations in non-compliant documents', async () => {
      const document = {
        id: 'doc-123',
        content: 'This document contains sensitive information including social security numbers and account numbers that should be protected.',
        documentType: 'contract' as const,
        uploadedAt: new Date(),
        status: 'pending' as const
      };

      const violations = await DocumentProcessing.scanForViolations(document);

      expect(violations.length).toBeGreaterThan(0);
      expect(DBOS.logger.info).toHaveBeenCalledWith(`Scanning document ${document.id} for compliance violations`);

      // Check that at least one critical violation was found
      const criticalViolations = violations.filter((v: ComplianceViolation) => v.severity === 'critical');
      expect(criticalViolations.length).toBeGreaterThan(0);
    });

    it('should not detect violations in compliant documents', async () => {
      // Override the mock for this specific test
      jest.spyOn(DocumentProcessing, 'analyzeViolationContext').mockResolvedValue(false);

      const document = {
        id: 'doc-456',
        content: 'This is a compliant document with no sensitive information.',
        documentType: 'policy' as const,
        uploadedAt: new Date(),
        status: 'pending' as const
      };

      const violations = await DocumentProcessing.scanForViolations(document);

      expect(violations.length).toBe(0);
    });
  });

  describe('notifyComplianceTeam', () => {
    it('should notify about critical violations', async () => {
      const violations = [
        {
          documentId: 'doc-123',
          ruleId: 'GLBA-001',
          violationType: 'privacy',
          description: 'Potential GLBA violation: Customer financial information must be protected',
          severity: 'critical' as const,
          recommendedAction: 'Immediate remediation required - escalate to legal team',
          detectedAt: new Date()
        }
      ];

      await DocumentProcessing.notifyComplianceTeam(violations);

      expect(DBOS.logger.warn).toHaveBeenCalledWith('CRITICAL: 1 critical violations detected');
    });

    it('should notify about high severity violations', async () => {
      const violations = [
        {
          documentId: 'doc-123',
          ruleId: 'SEC-001',
          violationType: 'financial_disclosure',
          description: 'Potential SEC violation: Financial statements must include quarterly earnings disclosure',
          severity: 'high' as const,
          recommendedAction: 'Priority remediation - update within 24 hours',
          detectedAt: new Date()
        }
      ];

      await DocumentProcessing.notifyComplianceTeam(violations);

      expect(DBOS.logger.warn).toHaveBeenCalledWith('HIGH: 1 high-severity violations detected');
    });
  });
});
