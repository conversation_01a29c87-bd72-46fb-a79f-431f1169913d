// Mock the console.log to prevent the "Cannot log after tests are done" warnings
const originalConsoleLog = console.log;
console.log = (...args) => {
  // Filter out server startup messages
  const message = args.join(' ');
  if (message.includes('Regulatory Compliance System running') || 
      message.includes('Compliance checking, KYC processing')) {
    return;
  }
  originalConsoleLog(...args);
};
