import { ReportGeneration } from '../src/workflows/report-generation';
import { ComplianceDatabase } from '../src/database/compliance-database';
import { DBOS } from '@dbos-inc/dbos-sdk';

// Mock DBOS SDK
jest.mock('@dbos-inc/dbos-sdk', () => {
  return {
    DBOS: {
      step: jest.fn().mockImplementation(() => (target: any, propertyKey: string) => {}),
      workflow: jest.fn().mockImplementation(() => (target: any, propertyKey: string) => {}),
      scheduled: jest.fn().mockImplementation(() => (target: any, propertyKey: string) => {}),
      transaction: jest.fn().mockImplementation(() => (target: any, propertyKey: string) => {}),
      sleep: jest.fn().mockResolvedValue(undefined),
      setEvent: jest.fn().mockResolvedValue(undefined),
      setConfig: jest.fn(),
      launch: jest.fn().mockResolvedValue(undefined),
      pgClient: {
        query: jest.fn().mockResolvedValue({ rows: [] })
      },
      logger: {
        info: jest.fn(),
        warn: jest.fn(),
        error: jest.fn()
      }
    },
    WorkflowQueue: jest.fn().mockImplementation(() => ({
      name: 'mock-queue'
    })),
    ConfiguredInstance: jest.fn()
  };
});

describe('Regulatory Monitoring', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock database methods
    jest.spyOn(ComplianceDatabase, 'getRegulatoryUpdates').mockResolvedValue([
      {
        id: 'SEC-2024-001',
        standard: 'SEC',
        title: 'Updated Cybersecurity Disclosure Requirements',
        description: 'New requirements for cybersecurity incident reporting',
        effectiveDate: new Date('2024-12-01'),
        impact: 'high',
        actionRequired: true
      }
    ]);
  });

  describe('fetchRegulatoryUpdates', () => {
    it('should fetch regulatory updates', async () => {
      const updates = await ReportGeneration.fetchRegulatoryUpdates();

      expect(Array.isArray(updates)).toBe(true);
      expect(updates.length).toBeGreaterThan(0);
      expect(DBOS.logger.info).toHaveBeenCalledWith('Fetching latest regulatory updates');

      // Verify update structure
      const firstUpdate = updates[0];
      expect(firstUpdate).toHaveProperty('id');
      expect(firstUpdate).toHaveProperty('standard');
      expect(firstUpdate).toHaveProperty('title');
      expect(firstUpdate).toHaveProperty('description');
      expect(firstUpdate).toHaveProperty('effectiveDate');
      expect(firstUpdate).toHaveProperty('impact');
      expect(firstUpdate).toHaveProperty('actionRequired');
    });
  });

  describe('analyzeRegulatoryImpact', () => {
    it('should generate recommendations based on regulatory updates', async () => {
      const updates = [
        {
          id: 'SEC-2024-001',
          standard: 'SEC',
          title: 'Updated Cybersecurity Disclosure Requirements',
          description: 'New requirements for cybersecurity incident reporting',
          effectiveDate: new Date('2024-12-01'),
          impact: 'high' as const,
          actionRequired: true
        },
        {
          id: 'GLBA-2024-002',
          standard: 'GLBA',
          title: 'Enhanced Privacy Notice Requirements',
          description: 'Updated privacy notice requirements',
          effectiveDate: new Date('2024-11-15'),
          impact: 'medium' as const,
          actionRequired: true
        },
        {
          id: 'SOX-2024-003',
          standard: 'SOX',
          title: 'Minor Documentation Update',
          description: 'Small clarification to existing requirements',
          effectiveDate: new Date('2024-10-01'),
          impact: 'low' as const,
          actionRequired: true
        }
      ];

      const recommendations = await ReportGeneration.analyzeRegulatoryImpact(updates);

      expect(Array.isArray(recommendations)).toBe(true);
      expect(recommendations.length).toBeGreaterThan(0);

      // Check for high impact recommendations
      const highImpactRecs = recommendations.filter((rec: string) => rec.includes('URGENT'));
      expect(highImpactRecs.length).toBeGreaterThan(0);

      // Check for medium impact recommendations
      const mediumImpactRecs = recommendations.filter((rec: string) => rec.includes('PRIORITY'));
      expect(mediumImpactRecs.length).toBeGreaterThan(0);

      // Check for low impact recommendations
      const lowImpactRecs = recommendations.filter((rec: string) => rec.includes('MONITOR'));
      expect(lowImpactRecs.length).toBeGreaterThan(0);
    });
  });
});
