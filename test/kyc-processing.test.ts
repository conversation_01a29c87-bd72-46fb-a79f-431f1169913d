import { KYCProcessing } from '../src/workflows/kyc-processing';
import { DBOS } from '@dbos-inc/dbos-sdk';

// Mock DBOS SDK
jest.mock('@dbos-inc/dbos-sdk', () => {
  return {
    DBOS: {
      step: jest.fn().mockImplementation(() => (target: any, propertyKey: string) => {}),
      workflow: jest.fn().mockImplementation(() => (target: any, propertyKey: string) => {}),
      scheduled: jest.fn().mockImplementation(() => (target: any, propertyKey: string) => {}),
      transaction: jest.fn().mockImplementation(() => (target: any, propertyKey: string) => {}),
      sleep: jest.fn().mockResolvedValue(undefined),
      setEvent: jest.fn().mockResolvedValue(undefined),
      setConfig: jest.fn(),
      launch: jest.fn().mockResolvedValue(undefined),
      pgClient: {
        query: jest.fn().mockResolvedValue({ rows: [] })
      },
      logger: {
        info: jest.fn(),
        warn: jest.fn(),
        error: jest.fn()
      }
    },
    WorkflowQueue: jest.fn().mockImplementation(() => ({
      name: 'mock-queue'
    })),
    ConfiguredInstance: jest.fn()
  };
});

describe('KYC Processing', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('verifyIdentity', () => {
    it('should verify valid identity with high confidence', async () => {
      const profile = {
        customerId: 'cust-123',
        personalInfo: {
          name: 'John Johnson',
          dateOfBirth: '1980-01-01',
          ssn: '***********',
          address: '123 Main Street, Anytown, USA 12345'
        },
        riskScore: 0,
        status: 'pending' as const,
        lastUpdated: new Date()
      };

      const result = await KYCProcessing.verifyIdentity(profile);

      expect(result.verified).toBe(true);
      expect(result.confidence).toBeGreaterThanOrEqual(0.8);
      expect(DBOS.logger.info).toHaveBeenCalledWith(`Verifying identity for customer ${profile.customerId}`);
    });

    it('should fail verification for invalid identity information', async () => {
      const profile = {
        customerId: 'cust-456',
        personalInfo: {
          name: 'Jane Smith',
          dateOfBirth: '2050-01-01', // Future date - invalid
          ssn: '123', // Invalid SSN
          address: 'Short' // Invalid address
        },
        riskScore: 0,
        status: 'pending' as const,
        lastUpdated: new Date()
      };

      const result = await KYCProcessing.verifyIdentity(profile);
      
      expect(result.verified).toBe(false);
      expect(result.confidence).toBeLessThan(0.8);
    });
  });

  describe('performRiskAssessment', () => {
    it('should calculate risk score based on profile data', async () => {
      const profile = {
        customerId: 'cust-123',
        personalInfo: {
          name: 'John Johnson',
          dateOfBirth: '1980-01-01', // Older customer - lower risk
          ssn: '***********',
          address: '123 Main Street, Anytown, USA 12345'
        },
        riskScore: 0,
        status: 'pending' as const,
        lastUpdated: new Date()
      };

      const riskScore = await KYCProcessing.performRiskAssessment(profile);

      expect(typeof riskScore).toBe('number');
      expect(riskScore).toBeGreaterThanOrEqual(0);
      expect(riskScore).toBeLessThanOrEqual(100);
      expect(DBOS.logger.info).toHaveBeenCalledWith(`Performing risk assessment for customer ${profile.customerId}`);
    });
  });

  describe('checkSanctionsList', () => {
    it('should identify customers on sanctions list', async () => {
      const profile = {
        customerId: 'cust-123',
        personalInfo: {
          name: 'John Doe', // Name on mock sanctions list
          dateOfBirth: '1980-01-01',
          ssn: '***********',
          address: '123 Main Street, Anytown, USA 12345'
        },
        riskScore: 0,
        status: 'pending' as const,
        lastUpdated: new Date()
      };

      const result = await KYCProcessing.checkSanctionsList(profile);

      expect(result.isListed).toBe(true);
      expect(result.details).toBeDefined();
    });

    it('should clear customers not on sanctions list', async () => {
      const profile = {
        customerId: 'cust-456',
        personalInfo: {
          name: 'Regular Customer', // Not on sanctions list
          dateOfBirth: '1980-01-01',
          ssn: '***********',
          address: '123 Main Street, Anytown, USA 12345'
        },
        riskScore: 0,
        status: 'pending' as const,
        lastUpdated: new Date()
      };

      const result = await KYCProcessing.checkSanctionsList(profile);
      
      expect(result.isListed).toBe(false);
      expect(result.details).toBeUndefined();
    });
  });
});
