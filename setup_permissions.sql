-- Setup Permissions for Compliance Command Center DBOS
-- Run this as a PostgreSQL superuser (e.g., postgres user)

-- Variables (replace with your actual values)
-- \set db_name compliance_command_center
-- \set app_user your_app_user

-- Create application user if it doesn't exist
-- Uncomment and modify the following lines if you need to create a new user:
-- CREATE USER your_app_user WITH PASSWORD 'your_secure_password';

-- Grant database connection privileges
-- GRANT CONNECT ON DATABASE :db_name TO :app_user;

-- Grant schema privileges
GRANT CREATE ON SCHEMA public TO PUBLIC;
GRANT USAGE ON SCHEMA public TO PUBLIC;

-- Grant default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO PUBLIC;

-- For existing objects (run after schema creation)
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO :app_user;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO :app_user;
-- GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO :app_user;

-- Alternative: Grant to specific user (recommended for production)
-- Replace 'your_app_user' with your actual application user
/*
GRANT CREATE ON SCHEMA public TO your_app_user;
GRANT USAGE ON SCHEMA public TO your_app_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO your_app_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO your_app_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO your_app_user;
*/

-- Verify permissions
SELECT 
    schemaname,
    tablename,
    tableowner,
    hasinsert,
    hasselect,
    hasupdate,
    hasdelete
FROM pg_tables 
WHERE schemaname = 'public'
LIMIT 5;

-- Show current user and permissions
SELECT current_user, session_user;
SELECT has_schema_privilege(current_user, 'public', 'CREATE') as can_create;
SELECT has_schema_privilege(current_user, 'public', 'USAGE') as can_use;
