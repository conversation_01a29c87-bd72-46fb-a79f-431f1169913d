
// API service for DBOS compliance endpoints
const API_BASE_URL = 'http://localhost:3000';

// Types and Interfaces
export interface ComplianceDocument {
  id: string;
  content: string;
  documentType: 'contract' | 'policy' | 'procedure' | 'financial_report';
  uploadedAt: Date;
  status: 'pending' | 'processing' | 'compliant' | 'non_compliant' | 'requires_review';
}

export interface ComplianceRule {
  id: string;
  standard: 'SEC' | 'GLBA' | 'SOX' | 'GDPR' | 'CCPA';
  ruleType: 'data_protection' | 'financial_disclosure' | 'privacy' | 'security';
  description: string;
  pattern: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface KYCProfile {
  customerId: string;
  personalInfo: {
    name: string;
    dateOfBirth: string;
    ssn: string;
    address: string;
  };
  riskScore: number;
  status: 'pending' | 'approved' | 'rejected' | 'under_review';
  lastUpdated: Date;
}

export interface ComplianceViolation {
  documentId: string;
  ruleId: string;
  violationType: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  recommendedAction: string;
  detectedAt: Date;
}

export interface ComplianceReport {
  id: string;
  reportType: 'monthly' | 'quarterly' | 'annual' | 'incident';
  generatedAt: Date;
  compliance_rate: number;
  violations: ComplianceViolation[];
  recommendations: string[];
}

export interface RegulatoryUpdate {
  id: string;
  standard: string;
  title: string;
  description: string;
  effectiveDate: Date;
  impact: 'low' | 'medium' | 'high';
  actionRequired: boolean;
}

export interface WorkflowStatus {
  id: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  currentStep: string;
  estimatedCompletion?: Date;
  result?: any;
}

// API Functions
export const complianceApi = {
  // Submit document for compliance check
  async submitDocument(documentData: {
    content: string;
    documentType: ComplianceDocument['documentType'];
    fileName?: string;
  }): Promise<{ workflowId: string; document: ComplianceDocument }> {
    const response = await fetch(`${API_BASE_URL}/api/compliance/document`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(documentData),
    });
    
    if (!response.ok) {
      throw new Error('Failed to submit document for compliance check');
    }
    
    return response.json();
  },

  // Submit customer for KYC processing
  async submitKYC(customerData: {
    personalInfo: KYCProfile['personalInfo'];
    additionalInfo?: any;
  }): Promise<{ workflowId: string; profile: KYCProfile }> {
    const response = await fetch(`${API_BASE_URL}/api/kyc/customer`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(customerData),
    });
    
    if (!response.ok) {
      throw new Error('Failed to submit customer for KYC processing');
    }
    
    return response.json();
  },

  // Generate compliance reports
  async generateReport(reportConfig: {
    reportType: ComplianceReport['reportType'];
    dateRange?: {
      startDate: string;
      endDate: string;
    };
    standards?: string[];
  }): Promise<{ workflowId: string; report: ComplianceReport }> {
    const response = await fetch(`${API_BASE_URL}/api/reports/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(reportConfig),
    });
    
    if (!response.ok) {
      throw new Error('Failed to generate compliance report');
    }
    
    return response.json();
  },

  // Get workflow progress
  async getWorkflowStatus(workflowId: string): Promise<WorkflowStatus> {
    const response = await fetch(`${API_BASE_URL}/api/workflow/${workflowId}/status`);
    
    if (!response.ok) {
      throw new Error('Failed to get workflow status');
    }
    
    return response.json();
  },

  // Get workflow results
  async getWorkflowResult(workflowId: string): Promise<any> {
    const response = await fetch(`${API_BASE_URL}/api/workflow/${workflowId}/result`);
    
    if (!response.ok) {
      throw new Error('Failed to get workflow result');
    }
    
    return response.json();
  }
};
