import { WorkflowQueue } from "@dbos-inc/dbos-sdk";

// Queues for different compliance processes
export const complianceQueue = new WorkflowQueue("compliance_checks", { 
  concurrency: 5, 
  rateLimit: { limitPerPeriod: 100, periodSec: 60 } 
});

export const kycQueue = new WorkflowQueue("kyc_processing", { 
  concurrency: 3,
  rateLimit: { limitPerPeriod: 50, periodSec: 60 }
});

export const reportingQueue = new WorkflowQueue("report_generation", {
  concurrency: 2
});
