import express from "express";
import path from 'path';
import fs from 'fs';
import { corsMiddleware } from '../middleware/cors';
import { errorHandler } from '../middleware/error-handler';

// Express app setup
export const app = express();

// Basic middleware
app.use(express.json());
app.use(corsMiddleware);

// Serve static files from dist directory
app.use(express.static(path.resolve('dist')));

// Handle static assets with specific extensions
app.get(/\.(js|css|svg|txt|ico|png|jpg|jpeg|gif|woff|woff2|ttf|eot)$/, (req, res): void => {
  const requestPath = req.path;
  console.log(`Static file request: ${requestPath}`);
  if(requestPath === '/') {
    console.log('Serving index.html');
    res.sendFile(path.resolve('dist', 'index.html'));
    return;
  }

  // Try to serve from dist/assets first, then from dist root
  const assetsPath = path.resolve('dist', 'assets', path.basename(requestPath));
  const rootPath = path.resolve('dist', requestPath.substring(1));

  // Check if file exists in assets directory first
  if (fs.existsSync(assetsPath)) {
    return res.sendFile(assetsPath);
  } else if (fs.existsSync(rootPath)) {
    return res.sendFile(rootPath);
  }

  // If file not found, return 404
  res.status(404).send('File not found');
});

// Health check endpoint
app.get('/health', (_req, res): void => {
  res.json({
    status: 'healthy',
    service: 'regulatory-compliance-system',
    timestamp: new Date().toISOString()
  });
});

// Error handling middleware (should be last)
app.use(errorHandler);
