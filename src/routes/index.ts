import { Express } from 'express';
import complianceRoutes from './compliance';
import kycRoutes from './kyc';
import reportsRoutes from './reports';
import dashboardRoutes from './dashboard';
import documentsRoutes from './documents';
import regulatoryRoutes from './regulatory';
import workflowsRoutes from './workflows';

export function setupRoutes(app: Express): void {
  // API route setup
  app.use('/api/compliance', complianceRoutes);
  app.use('/api/kyc', kycRoutes);
  app.use('/api/reports', reportsRoutes);
  app.use('/api/dashboard', dashboardRoutes);
  app.use('/api/documents', documentsRoutes);
  app.use('/api/regulatory', regulatoryRoutes);
  app.use('/api/workflow', workflowsRoutes);
}
