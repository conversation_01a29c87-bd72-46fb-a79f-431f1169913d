import { Router, Request, Response } from 'express';
import { ComplianceDatabase } from '../database';

const router = Router();

// Dashboard metrics
router.get('/metrics', async (_req: Request, res: Response): Promise<void> => {
  try {
    console.log('📊 Dashboard metrics requested');

    // Get real metrics from database
    const metrics = await ComplianceDatabase.getDashboardMetrics();

    res.json(metrics);
  } catch (error) {
    console.error('Error fetching dashboard metrics:', error);
    res.status(500).json({ error: 'Failed to fetch dashboard metrics' });
  }
});

// Compliance standards
router.get('/compliance-standards', async (_req: Request, res: Response): Promise<void> => {
  try {
    console.log('📋 Compliance standards requested');

    // Get real compliance standards from database
    const standards = await ComplianceDatabase.getComplianceStandards();

    res.json(standards);
  } catch (error) {
    console.error('Error fetching compliance standards:', error);
    res.status(500).json({ error: 'Failed to fetch compliance standards' });
  }
});

// Recent violations
router.get('/recent-violations', async (_req: Request, res: Response): Promise<void> => {
  try {
    console.log('🚨 Recent violations requested');

    // Get real violations from database
    const violations = await ComplianceDatabase.getRecentViolations();

    res.json(violations);
  } catch (error) {
    console.error('Error fetching recent violations:', error);
    res.status(500).json({ error: 'Failed to fetch recent violations' });
  }
});

// AI insights
router.get('/ai-insights', async (_req: Request, res: Response): Promise<void> => {
  try {
    console.log('🤖 AI insights requested');

    // Get real AI insights from database
    const insights = await ComplianceDatabase.getAIInsights();

    res.json(insights);
  } catch (error) {
    console.error('Error fetching AI insights:', error);
    res.status(500).json({ error: 'Failed to fetch AI insights' });
  }
});

// Workflow performance
router.get('/workflow-performance', async (_req: Request, res: Response): Promise<void> => {
  try {
    console.log('⚡ Workflow performance requested');

    // Get real workflow performance from database
    const performance = await ComplianceDatabase.getWorkflowPerformance();

    res.json(performance);
  } catch (error) {
    console.error('Error fetching workflow performance:', error);
    res.status(500).json({ error: 'Failed to fetch workflow performance' });
  }
});

// Violation details
router.get('/violation-details', (_req: Request, res: Response): void => {
  console.log('🚨 Violation details requested');
  // TODO: Implement database-driven violation details
  res.status(501).json({ error: 'Violation details endpoint not yet implemented with database' });
});

// Processing metrics
router.get('/processing-metrics', (_req: Request, res: Response): void => {
  console.log('⚡ Processing metrics requested');
  // TODO: Implement database-driven processing metrics
  res.status(501).json({ error: 'Processing metrics endpoint not yet implemented with database' });
});

export default router;
