import { Router, Request, Response } from 'express';
import { ComplianceDatabase } from '../database';

const router = Router();

// Get recent documents
router.get('/recent', async (_req: Request, res: Response): Promise<void> => {
  try {
    console.log('📄 Recent documents requested');

    // Get real documents from database
    const recentDocuments = await ComplianceDatabase.getRecentDocuments();

    res.json(recentDocuments);
  } catch (error) {
    console.error('Error fetching recent documents:', error);
    res.status(500).json({ error: 'Failed to fetch recent documents' });
  }
});

// Get document stats
router.get('/stats', async (_req: Request, res: Response): Promise<void> => {
  try {
    console.log('📊 Document stats requested');

    // Get real document statistics from database
    const stats = await ComplianceDatabase.getDocumentStats();

    res.json(stats);
  } catch (error) {
    console.error('Error fetching document stats:', error);
    res.status(500).json({ error: 'Failed to fetch document stats' });
  }
});

export default router;
