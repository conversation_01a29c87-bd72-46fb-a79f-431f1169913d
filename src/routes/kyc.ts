import { Router, Request, Response } from 'express';
import { DBOS } from "@dbos-inc/dbos-sdk";
import fs from 'fs';
import { KYCProfile } from '../types';
import { ComplianceSystem } from '../workflows';
import { ComplianceDatabase } from '../database';
import { kycQueue } from '../config/queues';
import { upload } from '../middleware/upload';

const router = Router();

// KYC customer processing endpoint
router.post('/customer', async (req: Request, res: Response): Promise<void> => {
  try {
    const profile: KYCProfile = req.body;

    // Start KYC processing workflow
    const handle = await DBOS.startWorkflow(
      ComplianceSystem,
      { queueName: kycQueue.name }
    ).processKYCCustomer(profile);

    // Generate a unique KYC ID
    const kycId = `KYC-${new Date().getFullYear()}-${String(Date.now()).slice(-3)}`;

    res.json({
      workflowId: handle.workflowID,
      kycId: kycId,
      status: 'kyc_processing_started',
      message: 'KYC verification initiated'
    });
  } catch (error) {
    DBOS.logger.error(`Error processing KYC: ${(error as Error).message}`);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// KYC documents upload endpoint
router.post('/documents', upload.array('documents'), async (req: any, res: Response): Promise<void> => {
  try {
    const files = req.files as any[];
    if (!files || files.length === 0) {
      res.status(400).json({ error: 'No documents uploaded' });
      return;
    }

    const { customerId } = req.body;

    // Process each document
    for (const file of files) {
      // In a real implementation, you would store the file and associate it with the customer
      DBOS.logger.info(`Processing KYC document: ${file.originalname} for customer: ${customerId}`);

      // Clean up uploaded file
      fs.unlinkSync(file.path);
    }

    res.json({
      workflowId: `KYC-DOC-${Date.now()}`,
      status: 'documents_uploaded',
      message: `${files.length} KYC documents uploaded successfully`
    });
  } catch (error) {
    DBOS.logger.error(`Error uploading KYC documents: ${(error as Error).message}`);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get KYC queue
router.get('/queue', async (_req: Request, res: Response): Promise<void> => {
  try {
    console.log('👥 KYC queue requested');

    // Get real KYC queue from database
    const kycQueue = await ComplianceDatabase.getKYCQueue();

    res.json(kycQueue);
  } catch (error) {
    console.error('Error fetching KYC queue:', error);
    res.status(500).json({ error: 'Failed to fetch KYC queue' });
  }
});

// Get KYC stats
router.get('/stats', (_req: Request, res: Response): void => {
  console.log('📊 KYC stats requested');
  // TODO: Implement database-driven KYC stats
  res.status(501).json({ error: 'KYC stats endpoint not yet implemented with database' });
});

export default router;
