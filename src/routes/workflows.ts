import { Router, Request, Response } from 'express';
import { DBOS } from "@dbos-inc/dbos-sdk";
import { ComplianceDatabase } from '../database';

const router = Router();

// Get workflow status
router.get('/:workflowId/status', async (req: Request, res: Response): Promise<void> => {
  try {
    const { workflowId } = req.params;

    if (!workflowId) {
      res.status(400).json({ error: 'Workflow ID is required' });
      return;
    }

    // Retrieve workflow handle and get status
    const handle = await DBOS.retrieveWorkflow(workflowId);
    const status = await handle.getStatus();

    // Check if status is null
    if (!status) {
      res.status(404).json({ error: 'Workflow status not available' });
      return;
    }

    // Get workflow events for detailed progress
    const events: Record<string, unknown> = {};
    try {
      events['processing_status'] = await DBOS.getEvent(workflowId, 'processing_status', 1);
      events['violations_found'] = await DBOS.getEvent(workflowId, 'violations_found', 1);
      events['kyc_status'] = await DBOS.getEvent(workflowId, 'kyc_status', 1);
      events['report_status'] = await DBOS.getEvent(workflowId, 'report_status', 1);
      events['final_status'] = await DBOS.getEvent(workflowId, 'final_status', 1);
    } catch (eventError) {
      // Events might not exist yet
      DBOS.logger.info(`No events found for workflow ${workflowId}`);
    }

    // Map DBOS status to frontend expected status
    let mappedStatus = status.status;
    DBOS.logger.info(`Raw DBOS status for workflow ${workflowId}: ${status.status}`);

    if (status.status === 'COMPLETED' || status.status === 'SUCCESS') {
      mappedStatus = 'completed';
      DBOS.logger.info(`Workflow ${workflowId} completed successfully - mapping to 'completed'`);
    } else if (status.status === 'ERROR' || status.status === 'FAILED') {
      mappedStatus = 'failed';
      DBOS.logger.info(`Workflow ${workflowId} failed - mapping to 'failed'`);
    } else if (status.status === 'PENDING' || status.status === 'ENQUEUED') {
      mappedStatus = 'pending';
    } else if (status.status === 'RUNNING') {
      mappedStatus = 'running';
    } else {
      DBOS.logger.warn(`Unknown DBOS status for workflow ${workflowId}: ${status.status} - mapping to original`);
    }

    res.json({
      workflowId,
      status: mappedStatus,
      workflowName: status.workflowName,
      events
    });
  } catch (error) {
    DBOS.logger.error(`Error getting workflow status: ${(error as Error).message}`);
    res.status(404).json({ error: 'Workflow not found' });
  }
});

// Get workflow result
router.get('/:workflowId/result', async (req: Request, res: Response): Promise<void> => {
  try {
    const { workflowId } = req.params;

    if (!workflowId) {
      res.status(400).json({ error: 'Workflow ID is required' });
      return;
    }

    // Retrieve workflow handle and get result
    const handle = await DBOS.retrieveWorkflow(workflowId);
    const result = await handle.getResult();

    res.json({
      workflowId,
      result
    });
  } catch (error) {
    DBOS.logger.error(`Error getting workflow result: ${(error as Error).message}`);
    res.status(404).json({ error: 'Workflow not found or not completed' });
  }
});

// Get active workflows
router.get('/active', async (_req: Request, res: Response): Promise<void> => {
  try {
    console.log('⚡ Active workflows requested');

    // Get real active workflows from database
    const activeWorkflows = await ComplianceDatabase.getActiveWorkflows();

    res.json(activeWorkflows);
  } catch (error) {
    console.error('Error fetching active workflows:', error);
    res.status(500).json({ error: 'Failed to fetch active workflows' });
  }
});

// Get workflow stats
router.get('/stats', (_req: Request, res: Response): void => {
  console.log('📊 Workflow stats requested');
  // TODO: Implement database-driven workflow stats
  res.status(501).json({ error: 'Workflow stats endpoint not yet implemented with database' });
});

// Get workflow metrics
router.get('/metrics', (_req: Request, res: Response): void => {
  console.log('📈 Workflow metrics requested');
  // TODO: Implement database-driven workflow metrics
  res.status(501).json({ error: 'Workflow metrics endpoint not yet implemented with database' });
});

export default router;
