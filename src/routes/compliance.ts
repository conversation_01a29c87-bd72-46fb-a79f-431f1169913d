import { Router, Request, Response } from 'express';
import { DBOS } from "@dbos-inc/dbos-sdk";
import fs from 'fs';
import { ComplianceDocument } from '../types';
import { ComplianceSystem } from '../workflows';
import { ComplianceDatabase } from '../database';
import { complianceQueue } from '../config/queues';
import { upload } from '../middleware/upload';

const router = Router();

// Document processing endpoints
router.post('/document', async (req: Request, res: Response): Promise<void> => {
  try {
    const document: ComplianceDocument = req.body;

    // Start compliance processing workflow
    const handle = await DBOS.startWorkflow(
      ComplianceSystem,
      { queueName: complianceQueue.name }
    ).processComplianceDocument(document);

    // Return workflow ID for tracking
    res.json({
      workflowId: handle.workflowID,
      status: 'processing_started',
      message: 'Document compliance check initiated'
    });
  } catch (error) {
    DBOS.logger.error(`Error processing document: ${(error as Error).message}`);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Document file upload endpoint
router.post('/document/upload', upload.single('file'), async (req: any, res: Response): Promise<void> => {
  try {
    if (!req.file) {
      res.status(400).json({ error: 'No file uploaded' });
      return;
    }

    const { documentType, documentId } = req.body;

    // Read file content
    const fileContent = fs.readFileSync(req.file.path, 'utf8');

    // Create document object
    const document: ComplianceDocument = {
      id: documentId || `DOC-${Date.now()}`,
      content: fileContent,
      documentType: documentType || 'procedure',
      uploadedAt: new Date(),
      status: 'pending'
    };

    // Start compliance processing workflow
    const handle = await DBOS.startWorkflow(
      ComplianceSystem,
      { queueName: complianceQueue.name }
    ).processComplianceDocument(document);

    // Clean up uploaded file
    fs.unlinkSync(req.file.path);

    res.json({
      workflowId: handle.workflowID,
      status: 'processing_started',
      message: 'Document uploaded and compliance check initiated'
    });
  } catch (error) {
    DBOS.logger.error(`Error uploading document: ${(error as Error).message}`);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get compliance violations summary
router.get('/violations/summary', (_req: Request, res: Response): void => {
  console.log('📊 Violation summary requested');
  // TODO: Implement database-driven violation summary
  res.status(501).json({ error: 'Violation summary endpoint not yet implemented with database' });
});

export default router;
