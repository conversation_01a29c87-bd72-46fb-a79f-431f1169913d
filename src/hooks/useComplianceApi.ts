
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { complianceApi, ComplianceDocument } from '@/services/complianceApi';
import { toast } from 'sonner';

// Document submission hook
export const useSubmitDocument = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: complianceApi.submitDocument,
    onSuccess: () => {
      toast.success('Document submitted for compliance check');
      queryClient.invalidateQueries({ queryKey: ['documents'] });
    },
    onError: (error) => {
      toast.error('Failed to submit document: ' + error.message);
    }
  });
};

// Document file upload hook
export const useSubmitDocumentFile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ file, documentType }: { file: File; documentType: ComplianceDocument['documentType'] }) =>
      complianceApi.submitDocumentFile(file, documentType),
    onSuccess: () => {
      toast.success('Document uploaded and submitted for compliance check');
      queryClient.invalidateQueries({ queryKey: ['documents'] });
    },
    onError: (error) => {
      toast.error('Failed to upload document: ' + error.message);
    }
  });
};

// KYC submission hook
export const useSubmitKYC = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: complianceApi.submitKYC,
    onSuccess: () => {
      toast.success('KYC processing initiated');
      queryClient.invalidateQueries({ queryKey: ['kyc'] });
    },
    onError: (error) => {
      toast.error('Failed to submit KYC: ' + error.message);
    }
  });
};

// KYC documents upload hook
export const useSubmitKYCDocuments = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ customerId, documents }: { customerId: string; documents: File[] }) =>
      complianceApi.submitKYCDocuments(customerId, documents),
    onSuccess: () => {
      toast.success('KYC documents uploaded successfully');
      queryClient.invalidateQueries({ queryKey: ['kyc'] });
    },
    onError: (error) => {
      toast.error('Failed to upload KYC documents: ' + error.message);
    }
  });
};

// Report generation hook
export const useGenerateReport = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: complianceApi.generateReport,
    onSuccess: () => {
      toast.success('Report generation started');
      queryClient.invalidateQueries({ queryKey: ['reports'] });
    },
    onError: (error) => {
      toast.error('Failed to generate report: ' + error.message);
    }
  });
};

// Regulatory impact analysis hook
export const useAnalyzeRegulatoryImpact = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: complianceApi.analyzeRegulatoryImpact,
    onSuccess: () => {
      toast.success('Regulatory impact analysis started');
      queryClient.invalidateQueries({ queryKey: ['regulatory'] });
    },
    onError: (error) => {
      toast.error('Failed to start impact analysis: ' + error.message);
    }
  });
};

// Workflow status hook
export const useWorkflowStatus = (workflowId: string | null, enabled: boolean = true) => {
  return useQuery({
    queryKey: ['workflow', workflowId, 'status'],
    queryFn: async () => {
      if (!workflowId) return null;

      console.log(`🔍 Fetching status for workflow: ${workflowId}`);
      const result = await complianceApi.getWorkflowStatus(workflowId);
      console.log(`📊 Workflow ${workflowId} status: ${result?.status}`);

      return result;
    },
    enabled: enabled && !!workflowId,
    refetchInterval: (query) => {
      // Don't poll if disabled
      if (!enabled) {
        console.log('🛑 Polling disabled for workflow:', workflowId);
        return false;
      }

      // Get the current data
      const workflowData = query.state.data;
      const currentStatus = workflowData?.status;

      // Stop polling if workflow is completed or failed
      if (currentStatus === 'completed') {
        console.log('🎉 Workflow completed successfully - STOPPING POLLING');
        return false;
      }

      if (currentStatus === 'failed') {
        console.log('❌ Workflow failed - STOPPING POLLING');
        return false;
      }

      // Continue polling every 2 seconds for faster updates
      console.log(`🔄 Workflow status: ${currentStatus} - continuing to poll...`);
      return 2000;
    },
    refetchOnWindowFocus: false, // Don't refetch when window gains focus
    staleTime: 0, // Always consider data stale to ensure fresh fetches
    gcTime: 0, // Don't cache data
  });
};

// Workflow result hook
export const useWorkflowResult = (workflowId: string | null, enabled: boolean = true) => {
  return useQuery({
    queryKey: ['workflow', workflowId, 'result'],
    queryFn: () => workflowId ? complianceApi.getWorkflowResult(workflowId) : null,
    enabled: enabled && !!workflowId
  });
};
