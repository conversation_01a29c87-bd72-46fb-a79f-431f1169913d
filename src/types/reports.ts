import { ComplianceViolation } from './compliance';

export interface ComplianceReport {
  id: string;
  reportType: 'monthly' | 'quarterly' | 'annual' | 'incident';
  generatedAt: Date;
  compliance_rate: number;
  violations: ComplianceViolation[];
  recommendations: string[];
}

// Extended interface for report generation requests
export interface ReportGenerationRequest {
  reportType: 'compliance-summary' | 'kyc-status' | 'violations-analysis' | 'regulatory-impact' | 'audit-trail';
  dateRange?: {
    startDate: string;
    endDate: string;
  };
  standards?: string[];
}

export interface RegulatoryUpdate {
  id: string;
  standard: string;
  title: string;
  description: string;
  effectiveDate: Date;
  impact: 'low' | 'medium' | 'high';
  actionRequired: boolean;
}
