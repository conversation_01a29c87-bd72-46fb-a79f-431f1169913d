
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Activity, Clock, CheckCircle, XCircle, Eye } from 'lucide-react';
import { useWorkflowStatus, useWorkflowResult } from '@/hooks/useComplianceApi';

interface WorkflowTrackerProps {
  workflowIds: string[];
}

const WorkflowTracker: React.FC<WorkflowTrackerProps> = ({ workflowIds }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Activity className="w-5 h-5 mr-2" />
          Active Workflows
        </CardTitle>
        <CardDescription>
          Real-time tracking of DBOS workflow execution
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {workflowIds.map((workflowId) => (
            <WorkflowItem key={workflowId} workflowId={workflowId} />
          ))}
          {workflowIds.length === 0 && (
            <p className="text-center text-gray-500 py-4">No active workflows</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

const WorkflowItem: React.FC<{ workflowId: string }> = ({ workflowId }) => {
  const { data: status, isLoading } = useWorkflowStatus(workflowId);
  const { data: result } = useWorkflowResult(
    workflowId, 
    status?.status === 'completed'
  );

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'running':
        return <Activity className="w-4 h-4 text-blue-500 animate-spin" />;
      default:
        return <Clock className="w-4 h-4 text-orange-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'running':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-orange-100 text-orange-800';
    }
  };

  if (isLoading) {
    return (
      <div className="p-4 border rounded-lg animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
        <div className="h-2 bg-gray-200 rounded w-full"></div>
      </div>
    );
  }

  if (!status) return null;

  return (
    <div className="flex items-center justify-between p-4 border rounded-lg">
      <div className="flex-1">
        <div className="flex items-center space-x-2 mb-2">
          {getStatusIcon(status.status)}
          <span className="font-medium text-sm">
            Workflow {workflowId.slice(0, 8)}...
          </span>
          <Badge className={getStatusColor(status.status)}>
            {status.status}
          </Badge>
        </div>
        
        <p className="text-sm text-gray-600 mb-2">{status.currentStep}</p>
        
        {status.status === 'running' && (
          <div className="space-y-1">
            <div className="flex justify-between text-xs">
              <span>Progress</span>
              <span>{status.progress}%</span>
            </div>
            <Progress value={status.progress} className="h-2" />
          </div>
        )}
        
        {status.estimatedCompletion && status.status === 'running' && (
          <p className="text-xs text-gray-500 mt-1">
            ETA: {new Date(status.estimatedCompletion).toLocaleTimeString()}
          </p>
        )}
      </div>
      
      <div className="flex space-x-2">
        {result && (
          <Button variant="outline" size="sm">
            <Eye className="w-4 h-4 mr-1" />
            View Result
          </Button>
        )}
      </div>
    </div>
  );
};

export default WorkflowTracker;
