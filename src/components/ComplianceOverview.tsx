
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { AlertTriangle, CheckCircle, Clock, Eye, FileText } from 'lucide-react';
import {
  complianceApi,
  ComplianceStandard,
  RecentViolation,
  AIInsight,
  WorkflowPerformance
} from '@/services/complianceApi';

const ComplianceOverview = () => {
  const [recentViolations, setRecentViolations] = useState<RecentViolation[]>([]);
  const [complianceStandards, setComplianceStandards] = useState<ComplianceStandard[]>([]);
  const [aiInsights, setAIInsights] = useState<AIInsight[]>([]);
  const [workflowPerformance, setWorkflowPerformance] = useState<WorkflowPerformance>({
    documentProcessing: 'Loading...',
    kycCompletionRate: 'Loading...',
    zeroDowntime: 'Loading...',
    costSavings: 'Loading...'
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [violations, standards, insights, performance] = await Promise.all([
          complianceApi.getRecentViolations(),
          complianceApi.getComplianceStandards(),
          complianceApi.getAIInsights(),
          complianceApi.getWorkflowPerformance()
        ]);

        setRecentViolations(violations);
        setComplianceStandards(standards);
        setAIInsights(insights);
        setWorkflowPerformance(performance);
      } catch (error) {
        console.error('Failed to fetch compliance overview data:', error);
        // Keep default values on error
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'Critical': return 'bg-red-100 text-red-800';
      case 'High': return 'bg-orange-100 text-orange-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getInsightColor = (color: string) => {
    switch (color) {
      case 'blue': return 'bg-blue-50 text-blue-900';
      case 'green': return 'bg-green-50 text-green-900';
      case 'orange': return 'bg-orange-50 text-orange-900';
      case 'red': return 'bg-red-50 text-red-900';
      default: return 'bg-gray-50 text-gray-900';
    }
  };

  const getInsightTextColor = (color: string) => {
    switch (color) {
      case 'blue': return 'text-blue-700';
      case 'green': return 'text-green-700';
      case 'orange': return 'text-orange-700';
      case 'red': return 'text-red-700';
      default: return 'text-gray-700';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Compliance Standards Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CheckCircle className="w-5 h-5 mr-2 text-green-500" />
            Compliance Standards Status
          </CardTitle>
          <CardDescription>
            Real-time compliance rates across all regulatory standards
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {complianceStandards.map((standard, index) => (
              <div key={index} className="p-4 border rounded-lg">
                <h4 className="font-semibold text-sm mb-2">{standard.name}</h4>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-2xl font-bold text-blue-600">{standard.compliance}%</span>
                  {standard.violations > 0 ? (
                    <Badge variant="destructive">{standard.violations} issues</Badge>
                  ) : (
                    <Badge variant="default" className="bg-green-100 text-green-800">✓ Compliant</Badge>
                  )}
                </div>
                <p className="text-xs text-gray-500">Last checked: {standard.lastCheck}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Violations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <AlertTriangle className="w-5 h-5 mr-2 text-red-500" />
            Recent Compliance Violations
          </CardTitle>
          <CardDescription>
            AI-detected violations requiring immediate attention
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentViolations.map((violation) => (
              <div key={violation.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <FileText className="w-4 h-4 text-gray-500" />
                    <span className="font-medium">{violation.document}</span>
                    <Badge className={getSeverityColor(violation.severity)}>
                      {violation.severity}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600 mb-1">{violation.violation}</p>
                  <div className="flex items-center space-x-4 text-xs text-gray-500">
                    <span>Detected: {violation.date}</span>
                    <span>Status: {violation.status}</span>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm">
                    <Eye className="w-4 h-4 mr-1" />
                    Review
                  </Button>
                  <Button variant="default" size="sm">
                    Remediate
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* AI Insights */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>AI Compliance Insights</CardTitle>
            <CardDescription>Automated analysis and recommendations</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {aiInsights.map((insight, index) => (
                <div key={index} className={`p-3 rounded-lg ${getInsightColor(insight.color)}`}>
                  <h4 className={`font-medium mb-1 ${getInsightColor(insight.color).replace('bg-', 'text-').replace('-50', '-900')}`}>
                    {insight.title}
                  </h4>
                  <p className={`text-sm ${getInsightTextColor(insight.color)}`}>
                    {insight.description}
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Workflow Performance</CardTitle>
            <CardDescription>DBOS-powered automation metrics</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Document Processing</span>
                <span className="text-lg font-bold text-green-600">{workflowPerformance.documentProcessing}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">KYC Completion Rate</span>
                <span className="text-lg font-bold text-blue-600">{workflowPerformance.kycCompletionRate}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Zero Downtime</span>
                <span className="text-lg font-bold text-purple-600">{workflowPerformance.zeroDowntime}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Cost Savings</span>
                <span className="text-lg font-bold text-green-600">{workflowPerformance.costSavings}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ComplianceOverview;
