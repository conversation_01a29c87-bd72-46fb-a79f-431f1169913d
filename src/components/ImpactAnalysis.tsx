import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { TrendingUp, AlertTriangle, FileText, Clock, CheckCircle, Target, BarChart3 } from 'lucide-react';
import { toast } from 'sonner';
import { useAnalyzeRegulatoryImpact, useWorkflowStatus } from '@/hooks/useComplianceApi';
import { complianceApi } from '@/services/complianceApi';

const ImpactAnalysis = () => {
  const [selectedUpdates, setSelectedUpdates] = useState<string[]>([]);
  const [currentWorkflowId, setCurrentWorkflowId] = useState<string | null>(null);
  const [analysisResults, setAnalysisResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  const analyzeImpactMutation = useAnalyzeRegulatoryImpact();
  const { data: workflowStatus } = useWorkflowStatus(currentWorkflowId);

  const regulatoryUpdates = [
    {
      id: 'sec-cyber-2024',
      name: 'SEC Cybersecurity Disclosure Requirements 2024',
      description: 'Enhanced cybersecurity incident reporting within 4 business days',
      impact: 'High',
      effectiveDate: '2024-09-01',
      affectedDocuments: 23,
      estimatedCost: '$125,000'
    },
    {
      id: 'glba-privacy-2024',
      name: 'GLBA Enhanced Privacy Notice Requirements',
      description: 'Updated consumer privacy notice standards for financial institutions',
      impact: 'Medium',
      effectiveDate: '2024-08-15',
      affectedDocuments: 15,
      estimatedCost: '$75,000'
    },
    {
      id: 'finra-digital-2024',
      name: 'FINRA Digital Asset Trading Guidelines',
      description: 'New guidance on cryptocurrency and digital asset trading compliance',
      impact: 'Low',
      effectiveDate: '2024-07-01',
      affectedDocuments: 8,
      estimatedCost: '$25,000'
    },
    {
      id: 'sox-controls-2024',
      name: 'SOX Internal Control Assessment Updates',
      description: 'Clarifications on management assessment of internal controls',
      impact: 'Medium',
      effectiveDate: '2024-12-31',
      affectedDocuments: 31,
      estimatedCost: '$95,000'
    },
    {
      id: 'ccpa-amendment-2024',
      name: 'CCPA Consumer Rights Amendment',
      description: 'New consumer data deletion and portability requirements',
      impact: 'High',
      effectiveDate: '2024-08-01',
      affectedDocuments: 18,
      estimatedCost: '$150,000'
    }
  ];

  useEffect(() => {
    // Simulate loading analysis results
    setTimeout(() => {
      setAnalysisResults([
        {
          id: 1,
          updateId: 'sec-cyber-2024',
          status: 'Completed',
          riskLevel: 'High',
          affectedDocuments: 23,
          complianceGap: 'Critical',
          recommendations: [
            'Update incident response procedures',
            'Implement automated reporting system',
            'Train security team on new requirements'
          ]
        },
        {
          id: 2,
          updateId: 'glba-privacy-2024',
          status: 'In Progress',
          riskLevel: 'Medium',
          affectedDocuments: 15,
          complianceGap: 'Moderate',
          recommendations: [
            'Revise privacy notice templates',
            'Update customer communication processes'
          ]
        }
      ]);
      setLoading(false);
    }, 2000);
  }, []);

  const handleAnalyzeImpact = async () => {
    if (selectedUpdates.length === 0) {
      toast.error('Please select at least one regulatory update to analyze');
      return;
    }

    try {
      const result = await analyzeImpactMutation.mutateAsync({
        regulatoryUpdates: selectedUpdates,
        timeframe: '6-months'
      });

      setCurrentWorkflowId(result.workflowId);
      
    } catch (error) {
      console.error('Impact analysis failed:', error);
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'High': return 'bg-red-100 text-red-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'Critical': return 'bg-red-100 text-red-800';
      case 'High': return 'bg-orange-100 text-orange-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const totalEstimatedCost = selectedUpdates.reduce((total, updateId) => {
    const update = regulatoryUpdates.find(u => u.id === updateId);
    return total + (update ? parseInt(update.estimatedCost.replace(/[$,]/g, '')) : 0);
  }, 0);

  const totalAffectedDocuments = selectedUpdates.reduce((total, updateId) => {
    const update = regulatoryUpdates.find(u => u.id === updateId);
    return total + (update ? update.affectedDocuments : 0);
  }, 0);

  return (
    <div className="space-y-6">
      {/* Analysis Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Selected Updates</p>
                <p className="text-2xl font-bold text-blue-600">{selectedUpdates.length}</p>
              </div>
              <Target className="w-8 h-8 text-blue-500" />
            </div>
            <p className="text-xs text-gray-500 mt-1">For analysis</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Affected Documents</p>
                <p className="text-2xl font-bold text-orange-600">{totalAffectedDocuments}</p>
              </div>
              <FileText className="w-8 h-8 text-orange-500" />
            </div>
            <p className="text-xs text-gray-500 mt-1">Require updates</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Estimated Cost</p>
                <p className="text-2xl font-bold text-red-600">
                  ${totalEstimatedCost.toLocaleString()}
                </p>
              </div>
              <BarChart3 className="w-8 h-8 text-red-500" />
            </div>
            <p className="text-xs text-gray-500 mt-1">Implementation cost</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Analysis Status</p>
                <p className="text-2xl font-bold text-green-600">
                  {workflowStatus ? `${workflowStatus.progress}%` : 'Ready'}
                </p>
              </div>
              <TrendingUp className="w-8 h-8 text-green-500" />
            </div>
            <p className="text-xs text-gray-500 mt-1">
              {workflowStatus ? 'In progress' : 'Awaiting start'}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Regulatory Updates Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <AlertTriangle className="w-5 h-5 mr-2" />
            Regulatory Impact Analysis
          </CardTitle>
          <CardDescription>
            Select regulatory updates to analyze their impact on your compliance posture
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {regulatoryUpdates.map((update) => (
              <div key={update.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <input
                    type="checkbox"
                    id={update.id}
                    checked={selectedUpdates.includes(update.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedUpdates(prev => [...prev, update.id]);
                      } else {
                        setSelectedUpdates(prev => prev.filter(id => id !== update.id));
                      }
                    }}
                    className="rounded"
                  />
                  <div className="flex-1">
                    <h4 className="font-medium">{update.name}</h4>
                    <p className="text-sm text-gray-600 mt-1">{update.description}</p>
                    <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                      <span>Effective: {update.effectiveDate}</span>
                      <span>•</span>
                      <span>{update.affectedDocuments} documents</span>
                      <span>•</span>
                      <span>Cost: {update.estimatedCost}</span>
                    </div>
                  </div>
                </div>
                <Badge className={getImpactColor(update.impact)}>
                  {update.impact} Impact
                </Badge>
              </div>
            ))}
          </div>

          <div className="flex justify-between items-center mt-6">
            <div className="text-sm text-gray-600">
              {selectedUpdates.length > 0 && (
                <span>
                  Selected: {selectedUpdates.length} update(s) • 
                  {totalAffectedDocuments} documents • 
                  ${totalEstimatedCost.toLocaleString()} estimated cost
                </span>
              )}
            </div>
            <Button 
              onClick={handleAnalyzeImpact} 
              disabled={analyzeImpactMutation.isPending || selectedUpdates.length === 0}
            >
              {analyzeImpactMutation.isPending ? (
                <>
                  <Clock className="w-4 h-4 mr-2 animate-spin" />
                  Analyzing...
                </>
              ) : (
                <>
                  <TrendingUp className="w-4 h-4 mr-2" />
                  Analyze Impact
                </>
              )}
            </Button>
          </div>

          {/* Workflow Status */}
          {workflowStatus && (
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-blue-900">
                  {workflowStatus.currentStep}
                </span>
                <span className="text-sm text-blue-700">{workflowStatus.progress}%</span>
              </div>
              <Progress value={workflowStatus.progress} className="mb-2" />
              <p className="text-xs text-blue-600">
                Status: {workflowStatus.status}
                {workflowStatus.estimatedCompletion && (
                  <span> • ETA: {new Date(workflowStatus.estimatedCompletion).toLocaleTimeString()}</span>
                )}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Analysis Results */}
      {analysisResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Analysis Results</CardTitle>
            <CardDescription>
              Impact analysis results and recommendations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analysisResults.map((result) => {
                const update = regulatoryUpdates.find(u => u.id === result.updateId);
                return (
                  <div key={result.id} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium">{update?.name}</h4>
                      <div className="flex items-center space-x-2">
                        <Badge className={getRiskColor(result.complianceGap)}>
                          {result.complianceGap} Gap
                        </Badge>
                        <Badge className={result.status === 'Completed' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'}>
                          {result.status === 'Completed' && <CheckCircle className="w-3 h-3 mr-1" />}
                          {result.status === 'In Progress' && <Clock className="w-3 h-3 mr-1" />}
                          {result.status}
                        </Badge>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div className="text-center p-3 bg-gray-50 rounded">
                        <p className="text-sm text-gray-600">Risk Level</p>
                        <p className="font-semibold text-lg">{result.riskLevel}</p>
                      </div>
                      <div className="text-center p-3 bg-gray-50 rounded">
                        <p className="text-sm text-gray-600">Affected Documents</p>
                        <p className="font-semibold text-lg">{result.affectedDocuments}</p>
                      </div>
                      <div className="text-center p-3 bg-gray-50 rounded">
                        <p className="text-sm text-gray-600">Compliance Gap</p>
                        <p className="font-semibold text-lg">{result.complianceGap}</p>
                      </div>
                    </div>

                    <div>
                      <h5 className="font-medium mb-2">Recommendations:</h5>
                      <ul className="space-y-1">
                        {result.recommendations.map((rec: string, index: number) => (
                          <li key={index} className="flex items-start space-x-2 text-sm">
                            <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                            <span>{rec}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ImpactAnalysis;
