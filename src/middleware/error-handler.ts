import { Request, Response, NextFunction } from "express";
import { DBOS } from "@dbos-inc/dbos-sdk";

export const errorHandler = (error: Error, req: Request, res: Response, next: NextFunction) => {
  DBOS.logger.error(`Error in ${req.method} ${req.path}: ${error.message}`);
  
  if (res.headersSent) {
    return next(error);
  }
  
  res.status(500).json({ 
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  });
};
