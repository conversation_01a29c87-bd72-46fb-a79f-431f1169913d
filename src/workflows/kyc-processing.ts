import { DBOS } from "@dbos-inc/dbos-sdk";
import { KYCProfile } from '../types';

export class KYCProcessing {
  
  // KYC Processing Steps
  @DBOS.step()
  static async verifyIdentity(profile: KYCProfile): Promise<{ verified: boolean; confidence: number }> {
    DBOS.logger.info(`Verifying identity for customer ${profile.customerId}`);

    // Simulate identity verification with random processing time (2-5 seconds)
    const processingTime = Math.floor(Math.random() * 3000) + 2000;
    await DBOS.sleep(processingTime);

    // Mock verification logic
    const hasValidSSN = profile.personalInfo.ssn && profile.personalInfo.ssn.length >= 9; // Allow for various SSN formats
    const hasValidDOB = profile.personalInfo.dateOfBirth && new Date(profile.personalInfo.dateOfBirth) < new Date();
    const hasValidAddress = profile.personalInfo.address && profile.personalInfo.address.length > 10;
    const hasValidName = profile.personalInfo.name && profile.personalInfo.name.length > 2;

    const confidence = (hasValidSSN ? 0.3 : 0) +
                      (hasValidDOB ? 0.25 : 0) +
                      (hasValidAddress ? 0.25 : 0) +
                      (hasValidName ? 0.2 : 0);

    const verified = confidence >= 0.8;

    DBOS.logger.info(`Identity verification completed: ${verified ? 'PASSED' : 'FAILED'} (${confidence.toFixed(2)}) after ${processingTime}ms`);
    return { verified, confidence };
  }

  @DBOS.step()
  static async performRiskAssessment(profile: KYCProfile): Promise<number> {
    DBOS.logger.info(`Performing risk assessment for customer ${profile.customerId}`);
    
    // Simulate risk assessment with random processing time (1-4 seconds)
    const processingTime = Math.floor(Math.random() * 3000) + 1000;
    await DBOS.sleep(processingTime);
    
    let riskScore = 0;
    
    // Age-based risk (younger = higher risk)
    const age = new Date().getFullYear() - new Date(profile.personalInfo.dateOfBirth).getFullYear();
    if (age < 25) riskScore += 20;
    else if (age < 35) riskScore += 10;
    
    // Address-based risk (simplified)
    const highRiskZipPrefixes = ['900', '800', '700']; // Mock high-risk areas
    const zipCode = profile.personalInfo.address.match(/\d{5}/)?.[0];
    if (zipCode && highRiskZipPrefixes.some(prefix => zipCode.startsWith(prefix))) {
      riskScore += 30;
    }
    
    // Random factor for demonstration
    riskScore += Math.floor(Math.random() * 20);
    
    DBOS.logger.info(`Risk assessment completed: score ${riskScore} after ${processingTime}ms`);
    return Math.min(riskScore, 100);
  }

  @DBOS.step()
  static async checkSanctionsList(profile: KYCProfile): Promise<{ isListed: boolean; details?: string }> {
    DBOS.logger.info(`Checking sanctions list for customer ${profile.customerId}`);
    
    // Simulate sanctions list check with random processing time (1-3 seconds)
    const processingTime = Math.floor(Math.random() * 2000) + 1000;
    await DBOS.sleep(processingTime);
    
    // Mock sanctions check - in production, this would query OFAC, UN, etc.
    const sanctionedNames = ['John Doe', 'Jane Smith']; // Mock list
    const isListed = sanctionedNames.includes(profile.personalInfo.name);
    
    const result = {
      isListed,
      details: isListed ? 'Found match in OFAC sanctions list' : undefined
    };
    
    DBOS.logger.info(`Sanctions check completed: ${isListed ? 'MATCH FOUND' : 'CLEAR'} after ${processingTime}ms`);
    return result;
  }
}
