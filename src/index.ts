import { DBOS } from "@dbos-inc/dbos-sdk";
import { app } from './config/express';
import { setupRoutes } from './routes';

// Setup API routes
setupRoutes(app);

// Main function
async function main() {
  // Configuration is loaded from dbos-config.yaml
  console.log(`🏛️  Regulatory Compliance System starting...`);
  console.log(`📊 Compliance checking, KYC processing, and regulatory monitoring active`);

  // Launch DBOS with Express app
  await DBOS.launch({ expressApp: app });

  // Start Express server on port 3000
  const PORT = process.env.PORT || 3000;
  app.listen(PORT, () => {
    console.log(`🏛️  Regulatory Compliance System running on http://localhost:${PORT}`);
    console.log(`📊 API endpoints available at http://localhost:${PORT}/api/*`);
    console.log(`🔍 Health check: http://localhost:${PORT}/health`);
  });
}

main().catch(console.log);