# Mock Data Loading Scripts

This directory contains scripts to load mock data from `src/index.ts` into the database according to the `database_schema.sql` structure.

## Files

- **`load_mock_data.sql`** - SQL script containing all mock data insertions
- **`load_mock_data.js`** - Node.js script to execute the SQL and provide verification
- **`README.md`** - This documentation file

## Quick Start

### Prerequisites

1. **Database Setup**: Ensure your PostgreSQL database is running and the schema has been created:
   ```bash
   # Create database (if not exists)
   createdb dbos_kyc_demo
   
   # Apply schema
   psql -d dbos_kyc_demo -f database_schema.sql
   ```

2. **Node.js Dependencies**: Install required packages:
   ```bash
   npm install pg
   ```

### Loading Mock Data

#### Option 1: Using Node.js Script (Recommended)

```bash
# Basic usage
node scripts/load_mock_data.js

# With verbose output
node scripts/load_mock_data.js --verbose

# Force clear existing data and reload
node scripts/load_mock_data.js --force --verbose

# Show help
node scripts/load_mock_data.js --help
```

#### Option 2: Using SQL Directly

```bash
# Set encryption key and run SQL
psql -d dbos_kyc_demo -c "SET app.encryption_key = 'demo_key';" -f scripts/load_mock_data.sql
```

### Environment Variables

The script supports standard PostgreSQL environment variables:

```bash
# Using individual variables
export PGHOST=localhost
export PGPORT=5432
export PGDATABASE=dbos_kyc_demo
export PGUSER=postgres
export PGPASSWORD=your_password

# Or using connection string
export DBOS_DATABASE_URL="************************************/database"

# Then run the script
node scripts/load_mock_data.js
```

## What Data Gets Loaded

The script loads the following mock data from `src/index.ts`:

### 1. Compliance Rules (3 records)
- **SEC-001**: Financial statements quarterly earnings disclosure
- **GLBA-001**: Customer financial information protection
- **SOX-001**: Internal controls documentation

### 2. Regulatory Updates (2 records)
- **SEC-2024-001**: Updated Cybersecurity Disclosure Requirements
- **GLBA-2024-002**: Enhanced Privacy Notice Requirements

### 3. Sample Documents (5 records)
- Q3_Financial_Report.pdf (non-compliant)
- Risk_Assessment_June.docx (compliant)
- Customer_Agreement_v3.pdf (processing)
- Policy_Update_May.pdf (compliant)
- Audit_Report_Q2.pdf (non-compliant)

### 4. KYC Profiles (7 records)
- John Anderson (under review, low risk)
- Sarah Michelle Corp (under review, medium risk)
- Robert Chen (under review, high risk)
- Emma Technologies Ltd (approved, low risk)
- Michael Rodriguez (under review, medium risk)
- Global Finance Inc (under review, medium risk)
- Alice Johnson (approved, low risk)

### 5. Compliance Violations (3 records)
- Missing SOX disclosure statement (critical)
- GLBA privacy notice incomplete (high)
- SEC reporting format non-compliance (medium)

### 6. Compliance Reports (5 records)
- Q2 2024 Compliance Summary
- June KYC Status Report
- May Violations Analysis
- Q2 Regulatory Impact Assessment
- May Audit Trail Report

### 7. Workflow Executions (5 records)
- Document compliance checks (running/completed)
- KYC processing workflows
- Report generation workflows
- Regulatory monitoring workflows

### 8. Notifications (4 records)
- Critical violation alerts
- KYC review notifications
- Regulatory update alerts
- System alerts

### 9. Performance Metrics (12 records)
- Compliance rates and violation counts
- KYC processing times and automation rates
- Workflow success rates and execution times
- System uptime and response times

### 10. Audit Logs (7 records)
- Document uploads
- Violation detections
- KYC status changes
- Report generations
- Regulatory updates
- Workflow executions
- User logins

## Data Relationships

The mock data maintains proper foreign key relationships:

- **Violations** → linked to Documents and Rules
- **Reports** → can be linked to Violations via junction table
- **Workflow Executions** → linked to Documents, KYC Profiles, or Reports
- **Notifications** → linked to Documents, Violations, KYC Profiles, or Regulatory Updates
- **Audit Logs** → reference various entity types and workflows

## Security Notes

### Encryption

KYC personal information is encrypted using PostgreSQL's `pgp_sym_encrypt` function:
- Names, dates of birth, SSNs, and addresses are encrypted
- Demo encryption key: `'demo_key'` (change for production!)
- Decryption functions are available in the schema

### Demo Data

⚠️ **Important**: This is mock data for development/demo purposes only:
- SSNs are fake (*********** format)
- Addresses are fictional
- Names are sample data
- Do not use in production without proper data sanitization

## Troubleshooting

### Common Issues

1. **"Database schema not found"**
   ```bash
   # Apply the schema first
   psql -d dbos_kyc_demo -f database_schema.sql
   ```

2. **"Connection refused"**
   ```bash
   # Check if PostgreSQL is running
   pg_ctl status
   
   # Check connection parameters
   psql -d dbos_kyc_demo -c "SELECT version();"
   ```

3. **"Permission denied"**
   ```bash
   # Ensure user has proper permissions
   psql -d dbos_kyc_demo -c "GRANT ALL ON ALL TABLES IN SCHEMA public TO your_user;"
   ```

4. **"Encryption key not set"**
   - The script automatically sets the encryption key
   - For manual SQL execution, run: `SET app.encryption_key = 'demo_key';`

### Verification

After loading, verify the data:

```sql
-- Check record counts
SELECT 
  'compliance_rules' as table_name, COUNT(*) as records FROM compliance_rules
UNION ALL
SELECT 'compliance_documents', COUNT(*) FROM compliance_documents
UNION ALL
SELECT 'kyc_profiles', COUNT(*) FROM kyc_profiles
UNION ALL
SELECT 'compliance_violations', COUNT(*) FROM compliance_violations;

-- Check sample data
SELECT rule_id, standard, severity FROM compliance_rules;
SELECT document_id, status, file_name FROM compliance_documents;
SELECT customer_id, status, risk_score FROM kyc_profiles;
```

## Integration with Application

After loading the mock data:

1. **Start the DBOS application**:
   ```bash
   npm start
   ```

2. **Access the dashboard**: The loaded data will be visible in:
   - Compliance dashboard (`/api/dashboard/metrics`)
   - Document management (`/api/documents/recent`)
   - KYC queue (`/api/kyc/queue`)
   - Reports section (`/api/reports/recent`)
   - Workflow monitoring (`/api/workflows/active`)

3. **Test workflows**: You can trigger new workflows that will interact with the loaded data.

## Customization

To modify the mock data:

1. **Edit `load_mock_data.sql`** to change values or add new records
2. **Update `src/index.ts`** if you want to change the source data structure
3. **Re-run the loading script** with `--force` to reload data

## Production Considerations

When adapting for production:

1. **Change encryption keys** to secure values
2. **Remove or replace** mock data with real data
3. **Implement proper** data validation and sanitization
4. **Set up proper** backup and recovery procedures
5. **Configure appropriate** user permissions and access controls
