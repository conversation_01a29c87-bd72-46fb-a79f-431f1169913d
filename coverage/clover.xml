<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1749844453638" clover="3.2.0">
  <project timestamp="1749844453638" name="All files">
    <metrics statements="903" coveredstatements="177" conditionals="267" coveredconditionals="43" methods="159" coveredmethods="14" elements="1329" coveredelements="234" complexity="0" loc="903" ncloc="903" packages="11" files="30" classes="30"/>
    <package name="src">
      <metrics statements="10" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="index.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/index.ts">
        <metrics statements="10" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
      </file>
      <file name="vite-env.d.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/vite-env.d.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.components.ui">
      <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="use-toast.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/components/ui/use-toast.ts">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.config">
      <metrics statements="32" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="express.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/config/express.ts">
        <metrics statements="26" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="32" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="35" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/config/index.ts">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
      </file>
      <file name="queues.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/config/queues.ts">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.database">
      <metrics statements="106" coveredstatements="20" conditionals="112" coveredconditionals="0" methods="30" coveredmethods="0"/>
      <file name="compliance-database.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/database/compliance-database.ts">
        <metrics statements="105" coveredstatements="19" conditionals="112" coveredconditionals="0" methods="30" coveredmethods="0"/>
        <line num="1" count="4" type="stmt"/>
        <line num="12" count="4" type="stmt"/>
        <line num="15" count="4" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="34" count="4" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="50" count="4" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="63" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="76" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="77" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="103" count="4" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="138" count="4" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="157" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="159" count="0" type="stmt"/>
        <line num="168" count="4" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="179" count="4" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="201" count="4" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="230" count="4" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="279" count="4" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="301" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="302" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="303" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="305" count="0" type="stmt"/>
        <line num="315" count="4" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="346" count="0" type="stmt"/>
        <line num="347" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="348" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="349" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="350" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="351" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="353" count="0" type="stmt"/>
        <line num="362" count="4" type="stmt"/>
        <line num="368" count="0" type="stmt"/>
        <line num="399" count="0" type="stmt"/>
        <line num="400" count="0" type="stmt"/>
        <line num="403" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="404" count="0" type="stmt"/>
        <line num="413" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="414" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="415" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="416" count="0" type="stmt"/>
        <line num="417" count="0" type="stmt"/>
        <line num="426" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="427" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="428" count="0" type="stmt"/>
        <line num="437" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="438" count="0" type="stmt"/>
        <line num="460" count="0" type="stmt"/>
        <line num="464" count="4" type="stmt"/>
        <line num="465" count="0" type="stmt"/>
        <line num="492" count="0" type="stmt"/>
        <line num="517" count="4" type="stmt"/>
        <line num="518" count="0" type="stmt"/>
        <line num="555" count="0" type="stmt"/>
        <line num="568" count="4" type="stmt"/>
        <line num="569" count="0" type="stmt"/>
        <line num="606" count="0" type="stmt"/>
        <line num="607" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="608" count="0" type="stmt"/>
        <line num="609" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="613" count="0" type="stmt"/>
        <line num="636" count="4" type="stmt"/>
        <line num="637" count="0" type="stmt"/>
        <line num="658" count="0" type="stmt"/>
        <line num="670" count="4" type="stmt"/>
        <line num="671" count="0" type="stmt"/>
        <line num="690" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/database/index.ts">
        <metrics statements="1" coveredstatements="1" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="2" count="4" type="stmt"/>
      </file>
    </package>
    <package name="src.hooks">
      <metrics statements="117" coveredstatements="0" conditionals="28" coveredconditionals="0" methods="43" coveredmethods="0"/>
      <file name="use-toast.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/hooks/use-toast.ts">
        <metrics statements="53" coveredstatements="0" conditionals="16" coveredconditionals="0" methods="18" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="60" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="77" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="91" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="96" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="116" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="117" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="159" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="164" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="179" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
      </file>
      <file name="useComplianceApi.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/hooks/useComplianceApi.ts">
        <metrics statements="64" coveredstatements="0" conditionals="12" coveredconditionals="0" methods="25" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="106" count="0" type="stmt"/>
        <line num="109" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="120" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="151" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="152" count="0" type="stmt"/>
        <line num="154" count="0" type="cond" truecount="0" falsecount="2"/>
      </file>
    </package>
    <package name="src.lib">
      <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="utils.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/lib/utils.ts">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.middleware">
      <metrics statements="22" coveredstatements="0" conditionals="7" coveredconditionals="0" methods="3" coveredmethods="0"/>
      <file name="cors.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/middleware/cors.ts">
        <metrics statements="7" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="10" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
      </file>
      <file name="error-handler.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/middleware/error-handler.ts">
        <metrics statements="6" coveredstatements="0" conditionals="3" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="7" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="8" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/middleware/index.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="upload.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/middleware/upload.ts">
        <metrics statements="6" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="20" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.routes">
      <metrics statements="270" coveredstatements="0" conditionals="31" coveredconditionals="0" methods="30" coveredmethods="0"/>
      <file name="compliance.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/compliance.ts">
        <metrics statements="31" coveredstatements="0" conditionals="5" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
      </file>
      <file name="dashboard.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/dashboard.ts">
        <metrics statements="45" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
      </file>
      <file name="documents.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/documents.ts">
        <metrics statements="18" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/index.ts">
        <metrics statements="15" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
      </file>
      <file name="kyc.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/kyc.ts">
        <metrics statements="40" coveredstatements="0" conditionals="3" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
      </file>
      <file name="regulatory.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/regulatory.ts">
        <metrics statements="30" coveredstatements="0" conditionals="5" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
      </file>
      <file name="reports.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/reports.ts">
        <metrics statements="27" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="14" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
      </file>
      <file name="workflows.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/workflows.ts">
        <metrics statements="64" coveredstatements="0" conditionals="17" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.services">
      <metrics statements="112" coveredstatements="0" conditionals="25" coveredconditionals="0" methods="26" coveredmethods="0"/>
      <file name="complianceApi.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/services/complianceApi.ts">
        <metrics statements="112" coveredstatements="0" conditionals="25" coveredconditionals="0" methods="26" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="138" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="139" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="157" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="158" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="185" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="186" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="206" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="207" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="230" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="231" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="251" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="252" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="262" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="263" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="273" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="274" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="284" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="285" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="294" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="295" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="304" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="305" count="0" type="stmt"/>
        <line num="308" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="314" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="315" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="324" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="325" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="333" count="0" type="stmt"/>
        <line num="335" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="336" count="0" type="stmt"/>
        <line num="339" count="0" type="stmt"/>
        <line num="343" count="0" type="stmt"/>
        <line num="345" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="346" count="0" type="stmt"/>
        <line num="349" count="0" type="stmt"/>
        <line num="354" count="0" type="stmt"/>
        <line num="356" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="357" count="0" type="stmt"/>
        <line num="360" count="0" type="stmt"/>
        <line num="364" count="0" type="stmt"/>
        <line num="366" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="367" count="0" type="stmt"/>
        <line num="370" count="0" type="stmt"/>
        <line num="374" count="0" type="stmt"/>
        <line num="376" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="377" count="0" type="stmt"/>
        <line num="380" count="0" type="stmt"/>
        <line num="385" count="0" type="stmt"/>
        <line num="387" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="388" count="0" type="stmt"/>
        <line num="391" count="0" type="stmt"/>
        <line num="395" count="0" type="stmt"/>
        <line num="397" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="398" count="0" type="stmt"/>
        <line num="401" count="0" type="stmt"/>
        <line num="406" count="0" type="stmt"/>
        <line num="408" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="409" count="0" type="stmt"/>
        <line num="412" count="0" type="stmt"/>
        <line num="416" count="0" type="stmt"/>
        <line num="418" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="419" count="0" type="stmt"/>
        <line num="422" count="0" type="stmt"/>
        <line num="427" count="0" type="stmt"/>
        <line num="429" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="430" count="0" type="stmt"/>
        <line num="433" count="0" type="stmt"/>
        <line num="437" count="0" type="stmt"/>
        <line num="439" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="440" count="0" type="stmt"/>
        <line num="443" count="0" type="stmt"/>
        <line num="447" count="0" type="stmt"/>
        <line num="449" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="450" count="0" type="stmt"/>
        <line num="453" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.types">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/types/index.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.workflows">
      <metrics statements="225" coveredstatements="157" conditionals="58" coveredconditionals="43" methods="20" coveredmethods="14"/>
      <file name="compliance-system.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/workflows/compliance-system.ts">
        <metrics statements="88" coveredstatements="53" conditionals="10" coveredconditionals="6" methods="4" coveredmethods="2"/>
        <line num="1" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="16" count="2" type="stmt"/>
        <line num="19" count="2" type="stmt"/>
        <line num="22" count="2" type="stmt"/>
        <line num="25" count="2" type="stmt"/>
        <line num="26" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="27" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="2" type="stmt"/>
        <line num="37" count="2" type="stmt"/>
        <line num="39" count="2" type="stmt"/>
        <line num="42" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="43" count="1" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="47" count="2" type="stmt"/>
        <line num="49" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="52" count="2" type="stmt"/>
        <line num="53" count="2" type="stmt"/>
        <line num="55" count="2" type="stmt"/>
        <line num="57" count="2" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="66" count="2" type="stmt"/>
        <line num="68" count="2" type="stmt"/>
        <line num="71" count="2" type="stmt"/>
        <line num="72" count="2" type="stmt"/>
        <line num="75" count="2" type="stmt"/>
        <line num="77" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="78" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="90" count="2" type="stmt"/>
        <line num="93" count="2" type="stmt"/>
        <line num="94" count="2" type="stmt"/>
        <line num="96" count="2" type="stmt"/>
        <line num="99" count="2" type="stmt"/>
        <line num="101" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="102" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="116" count="2" type="stmt"/>
        <line num="118" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="119" count="1" type="stmt"/>
        <line num="120" count="1" type="stmt"/>
        <line num="121" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="125" count="1" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="130" count="2" type="stmt"/>
        <line num="131" count="2" type="stmt"/>
        <line num="132" count="2" type="stmt"/>
        <line num="134" count="2" type="stmt"/>
        <line num="135" count="2" type="stmt"/>
        <line num="137" count="2" type="stmt"/>
        <line num="139" count="2" type="stmt"/>
        <line num="143" count="1" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="180" count="1" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
      </file>
      <file name="document-processing.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/workflows/document-processing.ts">
        <metrics statements="57" coveredstatements="40" conditionals="16" coveredconditionals="9" methods="8" coveredmethods="6"/>
        <line num="1" count="3" type="stmt"/>
        <line num="3" count="3" type="stmt"/>
        <line num="5" count="3" type="stmt"/>
        <line num="9" count="3" type="stmt"/>
        <line num="10" count="2" type="stmt"/>
        <line num="13" count="2" type="stmt"/>
        <line num="16" count="2" type="cond" truecount="3" falsecount="0"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="26" count="3" type="stmt"/>
        <line num="27" count="2" type="stmt"/>
        <line num="29" count="2" type="stmt"/>
        <line num="32" count="2" type="stmt"/>
        <line num="35" count="2" type="stmt"/>
        <line num="37" count="2" type="stmt"/>
        <line num="38" count="2" type="stmt"/>
        <line num="39" count="2" type="stmt"/>
        <line num="41" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="43" count="1" type="stmt"/>
        <line num="49" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="50" count="1" type="stmt"/>
        <line num="63" count="2" type="stmt"/>
        <line num="66" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="67" count="1" type="stmt"/>
        <line num="70" count="2" type="stmt"/>
        <line num="74" count="3" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="103" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="113" count="3" type="stmt"/>
        <line num="114" count="2" type="stmt"/>
        <line num="117" count="2" type="stmt"/>
        <line num="119" count="2" type="stmt"/>
        <line num="120" count="2" type="stmt"/>
        <line num="122" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="123" count="1" type="stmt"/>
        <line num="127" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="128" count="1" type="stmt"/>
        <line num="132" count="2" type="stmt"/>
        <line num="137" count="1" type="cond" truecount="1" falsecount="4"/>
        <line num="139" count="1" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/workflows/index.ts">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
      </file>
      <file name="kyc-processing.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/workflows/kyc-processing.ts">
        <metrics statements="38" coveredstatements="37" conditionals="28" coveredconditionals="24" methods="4" coveredmethods="4"/>
        <line num="1" count="2" type="stmt"/>
        <line num="4" count="2" type="stmt"/>
        <line num="8" count="2" type="stmt"/>
        <line num="9" count="2" type="stmt"/>
        <line num="12" count="2" type="stmt"/>
        <line num="13" count="2" type="stmt"/>
        <line num="16" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="17" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="18" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="19" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="21" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="26" count="2" type="stmt"/>
        <line num="28" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="29" count="2" type="stmt"/>
        <line num="33" count="2" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="44" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="45" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="48" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="50" count="3" type="cond" truecount="2" falsecount="1"/>
        <line num="51" count="0" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="62" count="2" type="stmt"/>
        <line num="63" count="2" type="stmt"/>
        <line num="66" count="2" type="stmt"/>
        <line num="67" count="2" type="stmt"/>
        <line num="70" count="2" type="stmt"/>
        <line num="71" count="2" type="stmt"/>
        <line num="73" count="2" type="stmt"/>
        <line num="78" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="79" count="2" type="stmt"/>
      </file>
      <file name="report-generation.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/workflows/report-generation.ts">
        <metrics statements="38" coveredstatements="27" conditionals="4" coveredconditionals="4" methods="4" coveredmethods="2"/>
        <line num="1" count="2" type="stmt"/>
        <line num="3" count="2" type="stmt"/>
        <line num="5" count="2" type="stmt"/>
        <line num="9" count="2" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="23" count="2" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="31" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="32" count="3" type="cond" truecount="3" falsecount="0"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="53" count="2" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="72" count="2" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
