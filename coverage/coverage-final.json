{"/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/index.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/index.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 39}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 39}}, "3": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 17}}, "4": {"start": {"line": 11, "column": 2}, "end": {"line": 11, "column": 41}}, "5": {"start": {"line": 13, "column": 15}, "end": {"line": 13, "column": 39}}, "6": {"start": {"line": 14, "column": 2}, "end": {"line": 17, "column": 5}}, "7": {"start": {"line": 15, "column": 4}, "end": {"line": 15, "column": 89}}, "8": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 92}}, "9": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 26}}}, "fnMap": {"0": {"name": "main", "decl": {"start": {"line": 9, "column": 15}, "end": {"line": 9, "column": 19}}, "loc": {"start": {"line": 9, "column": 19}, "end": {"line": 18, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 14, "column": 19}, "end": {"line": 14, "column": 22}}, "loc": {"start": {"line": 14, "column": 24}, "end": {"line": 17, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 13, "column": 15}, "end": {"line": 13, "column": 39}}, "type": "binary-expr", "locations": [{"start": {"line": 13, "column": 15}, "end": {"line": 13, "column": 31}}, {"start": {"line": 13, "column": 35}, "end": {"line": 13, "column": 39}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/vite-env.d.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/vite-env.d.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/components/ui/use-toast.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/components/ui/use-toast.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 9}}, "1": {"start": {"line": 3, "column": 9}, "end": {"line": 1, "column": 19}}, "2": {"start": {"line": 3, "column": 19}, "end": {"line": 1, "column": 52}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 3, "column": 9}, "end": {"line": 3, "column": 17}}, "loc": {"start": {"line": 3, "column": 9}, "end": {"line": 1, "column": 19}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 3, "column": 19}, "end": {"line": 3, "column": 24}}, "loc": {"start": {"line": 3, "column": 19}, "end": {"line": 1, "column": 52}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/config/express.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/config/express.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 30}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 24}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 20}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 52}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 59}}, "5": {"start": {"line": 8, "column": 13}, "end": {"line": 8, "column": 29}}, "6": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 24}}, "7": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 24}}, "8": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 46}}, "9": {"start": {"line": 18, "column": 0}, "end": {"line": 40, "column": 3}}, "10": {"start": {"line": 19, "column": 22}, "end": {"line": 19, "column": 30}}, "11": {"start": {"line": 20, "column": 2}, "end": {"line": 20, "column": 53}}, "12": {"start": {"line": 21, "column": 2}, "end": {"line": 25, "column": 3}}, "13": {"start": {"line": 22, "column": 4}, "end": {"line": 22, "column": 38}}, "14": {"start": {"line": 23, "column": 4}, "end": {"line": 23, "column": 53}}, "15": {"start": {"line": 24, "column": 4}, "end": {"line": 24, "column": 11}}, "16": {"start": {"line": 28, "column": 21}, "end": {"line": 28, "column": 79}}, "17": {"start": {"line": 29, "column": 19}, "end": {"line": 29, "column": 65}}, "18": {"start": {"line": 32, "column": 2}, "end": {"line": 36, "column": 3}}, "19": {"start": {"line": 33, "column": 4}, "end": {"line": 33, "column": 36}}, "20": {"start": {"line": 34, "column": 9}, "end": {"line": 36, "column": 3}}, "21": {"start": {"line": 35, "column": 4}, "end": {"line": 35, "column": 34}}, "22": {"start": {"line": 39, "column": 2}, "end": {"line": 39, "column": 41}}, "23": {"start": {"line": 43, "column": 0}, "end": {"line": 49, "column": 3}}, "24": {"start": {"line": 44, "column": 2}, "end": {"line": 48, "column": 5}}, "25": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 22}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 18, "column": 71}, "end": {"line": 18, "column": 72}}, "loc": {"start": {"line": 18, "column": 90}, "end": {"line": 40, "column": 1}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 43, "column": 19}, "end": {"line": 43, "column": 20}}, "loc": {"start": {"line": 43, "column": 39}, "end": {"line": 49, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 21, "column": 2}, "end": {"line": 25, "column": 3}}, "type": "if", "locations": [{"start": {"line": 21, "column": 2}, "end": {"line": 25, "column": 3}}]}, "1": {"loc": {"start": {"line": 32, "column": 2}, "end": {"line": 36, "column": 3}}, "type": "if", "locations": [{"start": {"line": 32, "column": 2}, "end": {"line": 36, "column": 3}}, {"start": {"line": 34, "column": 9}, "end": {"line": 36, "column": 3}}]}, "2": {"loc": {"start": {"line": 34, "column": 9}, "end": {"line": 36, "column": 3}}, "type": "if", "locations": [{"start": {"line": 34, "column": 9}, "end": {"line": 36, "column": 3}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0], "1": [0, 0], "2": [0]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/config/index.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/config/index.ts", "statementMap": {"0": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 26}}, "1": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 25}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0}, "f": {}, "b": {}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/config/queues.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/config/queues.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 51}}, "1": {"start": {"line": 4, "column": 13}, "end": {"line": 7, "column": 3}}, "2": {"start": {"line": 9, "column": 13}, "end": {"line": 12, "column": 3}}, "3": {"start": {"line": 14, "column": 13}, "end": {"line": 16, "column": 3}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/database/compliance-database.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/database/compliance-database.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 16, "column": 19}, "end": {"line": 21, "column": 6}}, "2": {"start": {"line": 23, "column": 4}, "end": {"line": 30, "column": 8}}, "3": {"start": {"line": 23, "column": 42}, "end": {"line": 30, "column": 6}}, "4": {"start": {"line": 35, "column": 19}, "end": {"line": 44, "column": 40}}, "5": {"start": {"line": 46, "column": 4}, "end": {"line": 46, "column": 29}}, "6": {"start": {"line": 51, "column": 4}, "end": {"line": 51, "column": 40}}, "7": {"start": {"line": 51, "column": 33}, "end": {"line": 51, "column": 40}}, "8": {"start": {"line": 54, "column": 24}, "end": {"line": 54, "column": 71}}, "9": {"start": {"line": 54, "column": 56}, "end": {"line": 54, "column": 68}}, "10": {"start": {"line": 55, "column": 28}, "end": {"line": 55, "column": 53}}, "11": {"start": {"line": 57, "column": 4}, "end": {"line": 65, "column": 5}}, "12": {"start": {"line": 58, "column": 21}, "end": {"line": 60, "column": null}}, "13": {"start": {"line": 62, "column": 6}, "end": {"line": 64, "column": 7}}, "14": {"start": {"line": 63, "column": 8}, "end": {"line": 63, "column": 54}}, "15": {"start": {"line": 68, "column": 20}, "end": {"line": 68, "column": 63}}, "16": {"start": {"line": 68, "column": 52}, "end": {"line": 68, "column": 60}}, "17": {"start": {"line": 69, "column": 24}, "end": {"line": 69, "column": 49}}, "18": {"start": {"line": 71, "column": 4}, "end": {"line": 79, "column": 5}}, "19": {"start": {"line": 72, "column": 21}, "end": {"line": 74, "column": null}}, "20": {"start": {"line": 76, "column": 6}, "end": {"line": 78, "column": 7}}, "21": {"start": {"line": 77, "column": 8}, "end": {"line": 77, "column": 51}}, "22": {"start": {"line": 82, "column": 28}, "end": {"line": 83, "column": null}}, "23": {"start": {"line": 83, "column": 6}, "end": {"line": 83, "column": 68}}, "24": {"start": {"line": 86, "column": 4}, "end": {"line": 86, "column": 45}}, "25": {"start": {"line": 86, "column": 38}, "end": {"line": 86, "column": 45}}, "26": {"start": {"line": 88, "column": 19}, "end": {"line": 90, "column": 16}}, "27": {"start": {"line": 89, "column": 6}, "end": {"line": 89, "column": 122}}, "28": {"start": {"line": 92, "column": 19}, "end": {"line": 94, "column": 6}}, "29": {"start": {"line": 92, "column": 48}, "end": {"line": 94, "column": 6}}, "30": {"start": {"line": 96, "column": 4}, "end": {"line": 99, "column": 15}}, "31": {"start": {"line": 104, "column": 19}, "end": {"line": 132, "column": 6}}, "32": {"start": {"line": 134, "column": 4}, "end": {"line": 134, "column": 29}}, "33": {"start": {"line": 144, "column": 26}, "end": {"line": 151, "column": 6}}, "34": {"start": {"line": 153, "column": 16}, "end": {"line": 153, "column": 37}}, "35": {"start": {"line": 154, "column": 27}, "end": {"line": 154, "column": 61}}, "36": {"start": {"line": 155, "column": 31}, "end": {"line": 155, "column": 69}}, "37": {"start": {"line": 156, "column": 28}, "end": {"line": 156, "column": 63}}, "38": {"start": {"line": 157, "column": 27}, "end": {"line": 157, "column": 97}}, "39": {"start": {"line": 159, "column": 4}, "end": {"line": 164, "column": 6}}, "40": {"start": {"line": 169, "column": 19}, "end": {"line": 173, "column": 110}}, "41": {"start": {"line": 175, "column": 4}, "end": {"line": 175, "column": 29}}, "42": {"start": {"line": 180, "column": 19}, "end": {"line": 187, "column": 6}}, "43": {"start": {"line": 189, "column": 4}, "end": {"line": 197, "column": 8}}, "44": {"start": {"line": 189, "column": 42}, "end": {"line": 197, "column": 6}}, "45": {"start": {"line": 208, "column": 26}, "end": {"line": 217, "column": 6}}, "46": {"start": {"line": 219, "column": 16}, "end": {"line": 219, "column": 37}}, "47": {"start": {"line": 220, "column": 4}, "end": {"line": 226, "column": 6}}, "48": {"start": {"line": 231, "column": 19}, "end": {"line": 267, "column": 6}}, "49": {"start": {"line": 269, "column": 4}, "end": {"line": 275, "column": 8}}, "50": {"start": {"line": 269, "column": 42}, "end": {"line": 275, "column": 6}}, "51": {"start": {"line": 285, "column": 19}, "end": {"line": 298, "column": 6}}, "52": {"start": {"line": 300, "column": 16}, "end": {"line": 300, "column": 30}}, "53": {"start": {"line": 301, "column": 29}, "end": {"line": 301, "column": 65}}, "54": {"start": {"line": 302, "column": 31}, "end": {"line": 302, "column": 69}}, "55": {"start": {"line": 303, "column": 33}, "end": {"line": 303, "column": 78}}, "56": {"start": {"line": 305, "column": 4}, "end": {"line": 311, "column": 6}}, "57": {"start": {"line": 321, "column": 19}, "end": {"line": 344, "column": 6}}, "58": {"start": {"line": 346, "column": 16}, "end": {"line": 346, "column": 30}}, "59": {"start": {"line": 347, "column": 33}, "end": {"line": 347, "column": 87}}, "60": {"start": {"line": 348, "column": 30}, "end": {"line": 348, "column": 69}}, "61": {"start": {"line": 349, "column": 32}, "end": {"line": 349, "column": 71}}, "62": {"start": {"line": 350, "column": 27}, "end": {"line": 350, "column": 61}}, "63": {"start": {"line": 351, "column": 19}, "end": {"line": 351, "column": 91}}, "64": {"start": {"line": 353, "column": 4}, "end": {"line": 358, "column": 6}}, "65": {"start": {"line": 368, "column": 19}, "end": {"line": 397, "column": 6}}, "66": {"start": {"line": 399, "column": 16}, "end": {"line": 399, "column": 30}}, "67": {"start": {"line": 400, "column": 21}, "end": {"line": 400, "column": 23}}, "68": {"start": {"line": 403, "column": 4}, "end": {"line": 410, "column": 5}}, "69": {"start": {"line": 404, "column": 6}, "end": {"line": 409, "column": 9}}, "70": {"start": {"line": 413, "column": 24}, "end": {"line": 413, "column": 68}}, "71": {"start": {"line": 414, "column": 25}, "end": {"line": 414, "column": 70}}, "72": {"start": {"line": 415, "column": 4}, "end": {"line": 423, "column": 5}}, "73": {"start": {"line": 416, "column": 26}, "end": {"line": 416, "column": 76}}, "74": {"start": {"line": 417, "column": 6}, "end": {"line": 422, "column": 9}}, "75": {"start": {"line": 426, "column": 25}, "end": {"line": 426, "column": 63}}, "76": {"start": {"line": 427, "column": 4}, "end": {"line": 434, "column": 5}}, "77": {"start": {"line": 428, "column": 6}, "end": {"line": 433, "column": 9}}, "78": {"start": {"line": 437, "column": 4}, "end": {"line": 458, "column": 5}}, "79": {"start": {"line": 438, "column": 6}, "end": {"line": 457, "column": 8}}, "80": {"start": {"line": 460, "column": 4}, "end": {"line": 460, "column": 20}}, "81": {"start": {"line": 465, "column": 19}, "end": {"line": 490, "column": 6}}, "82": {"start": {"line": 492, "column": 4}, "end": {"line": 513, "column": 8}}, "83": {"start": {"line": 492, "column": 57}, "end": {"line": 513, "column": 6}}, "84": {"start": {"line": 518, "column": 19}, "end": {"line": 553, "column": 6}}, "85": {"start": {"line": 555, "column": 4}, "end": {"line": 564, "column": 8}}, "86": {"start": {"line": 555, "column": 42}, "end": {"line": 564, "column": 6}}, "87": {"start": {"line": 569, "column": 19}, "end": {"line": 604, "column": 6}}, "88": {"start": {"line": 606, "column": 4}, "end": {"line": 632, "column": 7}}, "89": {"start": {"line": 607, "column": 29}, "end": {"line": 607, "column": 65}}, "90": {"start": {"line": 608, "column": 32}, "end": {"line": 608, "column": 35}}, "91": {"start": {"line": 609, "column": 23}, "end": {"line": 611, "column": 41}}, "92": {"start": {"line": 613, "column": 6}, "end": {"line": 631, "column": 8}}, "93": {"start": {"line": 637, "column": 19}, "end": {"line": 656, "column": 6}}, "94": {"start": {"line": 658, "column": 4}, "end": {"line": 666, "column": 8}}, "95": {"start": {"line": 658, "column": 42}, "end": {"line": 666, "column": 6}}, "96": {"start": {"line": 671, "column": 19}, "end": {"line": 688, "column": 6}}, "97": {"start": {"line": 690, "column": 4}, "end": {"line": 697, "column": 8}}, "98": {"start": {"line": 690, "column": 57}, "end": {"line": 697, "column": 6}}, "99": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 13}}, "100": {"start": {"line": 15, "column": 15}, "end": {"line": 31, "column": null}}, "101": {"start": {"line": 34, "column": 15}, "end": {"line": 47, "column": null}}, "102": {"start": {"line": 50, "column": 15}, "end": {"line": 100, "column": null}}, "103": {"start": {"line": 103, "column": 15}, "end": {"line": 135, "column": null}}, "104": {"start": {"line": 138, "column": 15}, "end": {"line": 165, "column": null}}, "105": {"start": {"line": 168, "column": 15}, "end": {"line": 176, "column": null}}, "106": {"start": {"line": 179, "column": 15}, "end": {"line": 198, "column": null}}, "107": {"start": {"line": 201, "column": 15}, "end": {"line": 227, "column": null}}, "108": {"start": {"line": 230, "column": 15}, "end": {"line": 276, "column": null}}, "109": {"start": {"line": 279, "column": 15}, "end": {"line": 312, "column": null}}, "110": {"start": {"line": 315, "column": 15}, "end": {"line": 359, "column": null}}, "111": {"start": {"line": 362, "column": 15}, "end": {"line": 461, "column": null}}, "112": {"start": {"line": 464, "column": 15}, "end": {"line": 514, "column": null}}, "113": {"start": {"line": 517, "column": 15}, "end": {"line": 565, "column": null}}, "114": {"start": {"line": 568, "column": 15}, "end": {"line": 633, "column": null}}, "115": {"start": {"line": 636, "column": 15}, "end": {"line": 667, "column": null}}, "116": {"start": {"line": 670, "column": 15}, "end": {"line": 698, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": 8}}, "loc": {"start": {"line": 15, "column": 39}, "end": {"line": 31, "column": 3}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 23, "column": 27}, "end": {"line": 23, "column": 28}}, "loc": {"start": {"line": 23, "column": 42}, "end": {"line": 30, "column": 6}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 34, "column": 2}, "end": {"line": 34, "column": 8}}, "loc": {"start": {"line": 34, "column": 56}, "end": {"line": 47, "column": 3}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 50, "column": 2}, "end": {"line": 50, "column": 8}}, "loc": {"start": {"line": 50, "column": 63}, "end": {"line": 100, "column": 3}}}, "4": {"name": "(anonymous_6)", "decl": {"start": {"line": 54, "column": 51}, "end": {"line": 54, "column": 52}}, "loc": {"start": {"line": 54, "column": 56}, "end": {"line": 54, "column": 68}}}, "5": {"name": "(anonymous_7)", "decl": {"start": {"line": 68, "column": 47}, "end": {"line": 68, "column": 48}}, "loc": {"start": {"line": 68, "column": 52}, "end": {"line": 68, "column": 60}}}, "6": {"name": "(anonymous_8)", "decl": {"start": {"line": 82, "column": 46}, "end": {"line": 82, "column": 47}}, "loc": {"start": {"line": 83, "column": 6}, "end": {"line": 83, "column": 68}}}, "7": {"name": "(anonymous_9)", "decl": {"start": {"line": 88, "column": 39}, "end": {"line": 88, "column": 40}}, "loc": {"start": {"line": 89, "column": 6}, "end": {"line": 89, "column": 122}}}, "8": {"name": "(anonymous_10)", "decl": {"start": {"line": 92, "column": 43}, "end": {"line": 92, "column": 44}}, "loc": {"start": {"line": 92, "column": 48}, "end": {"line": 94, "column": 6}}}, "9": {"name": "(anonymous_11)", "decl": {"start": {"line": 103, "column": 2}, "end": {"line": 103, "column": 8}}, "loc": {"start": {"line": 103, "column": 49}, "end": {"line": 135, "column": 3}}}, "10": {"name": "(anonymous_12)", "decl": {"start": {"line": 138, "column": 2}, "end": {"line": 138, "column": 8}}, "loc": {"start": {"line": 138, "column": 35}, "end": {"line": 165, "column": 3}}}, "11": {"name": "(anonymous_13)", "decl": {"start": {"line": 168, "column": 2}, "end": {"line": 168, "column": 8}}, "loc": {"start": {"line": 168, "column": 60}, "end": {"line": 176, "column": 3}}}, "12": {"name": "(anonymous_14)", "decl": {"start": {"line": 179, "column": 2}, "end": {"line": 179, "column": 8}}, "loc": {"start": {"line": 179, "column": 35}, "end": {"line": 198, "column": 3}}}, "13": {"name": "(anonymous_15)", "decl": {"start": {"line": 189, "column": 27}, "end": {"line": 189, "column": 28}}, "loc": {"start": {"line": 189, "column": 42}, "end": {"line": 197, "column": 6}}}, "14": {"name": "(anonymous_16)", "decl": {"start": {"line": 201, "column": 2}, "end": {"line": 201, "column": 8}}, "loc": {"start": {"line": 201, "column": 34}, "end": {"line": 227, "column": 3}}}, "15": {"name": "(anonymous_17)", "decl": {"start": {"line": 230, "column": 2}, "end": {"line": 230, "column": 8}}, "loc": {"start": {"line": 230, "column": 37}, "end": {"line": 276, "column": 3}}}, "16": {"name": "(anonymous_18)", "decl": {"start": {"line": 269, "column": 27}, "end": {"line": 269, "column": 28}}, "loc": {"start": {"line": 269, "column": 42}, "end": {"line": 275, "column": 6}}}, "17": {"name": "(anonymous_19)", "decl": {"start": {"line": 279, "column": 2}, "end": {"line": 279, "column": 8}}, "loc": {"start": {"line": 279, "column": 31}, "end": {"line": 312, "column": 3}}}, "18": {"name": "(anonymous_20)", "decl": {"start": {"line": 315, "column": 2}, "end": {"line": 315, "column": 8}}, "loc": {"start": {"line": 315, "column": 37}, "end": {"line": 359, "column": 3}}}, "19": {"name": "(anonymous_21)", "decl": {"start": {"line": 362, "column": 2}, "end": {"line": 362, "column": 8}}, "loc": {"start": {"line": 362, "column": 28}, "end": {"line": 461, "column": 3}}}, "20": {"name": "(anonymous_22)", "decl": {"start": {"line": 464, "column": 2}, "end": {"line": 464, "column": 8}}, "loc": {"start": {"line": 464, "column": 26}, "end": {"line": 514, "column": 3}}}, "21": {"name": "(anonymous_23)", "decl": {"start": {"line": 492, "column": 27}, "end": {"line": 492, "column": 28}}, "loc": {"start": {"line": 492, "column": 57}, "end": {"line": 513, "column": 6}}}, "22": {"name": "(anonymous_24)", "decl": {"start": {"line": 517, "column": 2}, "end": {"line": 517, "column": 8}}, "loc": {"start": {"line": 517, "column": 31}, "end": {"line": 565, "column": 3}}}, "23": {"name": "(anonymous_25)", "decl": {"start": {"line": 555, "column": 27}, "end": {"line": 555, "column": 28}}, "loc": {"start": {"line": 555, "column": 42}, "end": {"line": 564, "column": 6}}}, "24": {"name": "(anonymous_26)", "decl": {"start": {"line": 568, "column": 2}, "end": {"line": 568, "column": 8}}, "loc": {"start": {"line": 568, "column": 33}, "end": {"line": 633, "column": 3}}}, "25": {"name": "(anonymous_27)", "decl": {"start": {"line": 606, "column": 27}, "end": {"line": 606, "column": 28}}, "loc": {"start": {"line": 606, "column": 55}, "end": {"line": 632, "column": 5}}}, "26": {"name": "(anonymous_28)", "decl": {"start": {"line": 636, "column": 2}, "end": {"line": 636, "column": 8}}, "loc": {"start": {"line": 636, "column": 33}, "end": {"line": 667, "column": 3}}}, "27": {"name": "(anonymous_29)", "decl": {"start": {"line": 658, "column": 27}, "end": {"line": 658, "column": 28}}, "loc": {"start": {"line": 658, "column": 42}, "end": {"line": 666, "column": 6}}}, "28": {"name": "(anonymous_30)", "decl": {"start": {"line": 670, "column": 2}, "end": {"line": 670, "column": 8}}, "loc": {"start": {"line": 670, "column": 34}, "end": {"line": 698, "column": 3}}}, "29": {"name": "(anonymous_31)", "decl": {"start": {"line": 690, "column": 27}, "end": {"line": 690, "column": 28}}, "loc": {"start": {"line": 690, "column": 57}, "end": {"line": 697, "column": 6}}}}, "branchMap": {"0": {"loc": {"start": {"line": 51, "column": 4}, "end": {"line": 51, "column": 40}}, "type": "if", "locations": [{"start": {"line": 51, "column": 4}, "end": {"line": 51, "column": 40}}]}, "1": {"loc": {"start": {"line": 62, "column": 6}, "end": {"line": 64, "column": 7}}, "type": "if", "locations": [{"start": {"line": 62, "column": 6}, "end": {"line": 64, "column": 7}}]}, "2": {"loc": {"start": {"line": 76, "column": 6}, "end": {"line": 78, "column": 7}}, "type": "if", "locations": [{"start": {"line": 76, "column": 6}, "end": {"line": 78, "column": 7}}]}, "3": {"loc": {"start": {"line": 83, "column": 6}, "end": {"line": 83, "column": 68}}, "type": "binary-expr", "locations": [{"start": {"line": 83, "column": 6}, "end": {"line": 83, "column": 39}}, {"start": {"line": 83, "column": 43}, "end": {"line": 83, "column": 68}}]}, "4": {"loc": {"start": {"line": 86, "column": 4}, "end": {"line": 86, "column": 45}}, "type": "if", "locations": [{"start": {"line": 86, "column": 4}, "end": {"line": 86, "column": 45}}]}, "5": {"loc": {"start": {"line": 154, "column": 27}, "end": {"line": 154, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 154, "column": 27}, "end": {"line": 154, "column": 56}}, {"start": {"line": 154, "column": 60}, "end": {"line": 154, "column": 61}}]}, "6": {"loc": {"start": {"line": 155, "column": 31}, "end": {"line": 155, "column": 69}}, "type": "binary-expr", "locations": [{"start": {"line": 155, "column": 31}, "end": {"line": 155, "column": 64}}, {"start": {"line": 155, "column": 68}, "end": {"line": 155, "column": 69}}]}, "7": {"loc": {"start": {"line": 156, "column": 28}, "end": {"line": 156, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 156, "column": 28}, "end": {"line": 156, "column": 58}}, {"start": {"line": 156, "column": 62}, "end": {"line": 156, "column": 63}}]}, "8": {"loc": {"start": {"line": 157, "column": 27}, "end": {"line": 157, "column": 97}}, "type": "cond-expr", "locations": [{"start": {"line": 157, "column": 48}, "end": {"line": 157, "column": 91}}, {"start": {"line": 157, "column": 94}, "end": {"line": 157, "column": 97}}]}, "9": {"loc": {"start": {"line": 221, "column": 22}, "end": {"line": 221, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 221, "column": 22}, "end": {"line": 221, "column": 53}}, {"start": {"line": 221, "column": 57}, "end": {"line": 221, "column": 61}}]}, "10": {"loc": {"start": {"line": 222, "column": 24}, "end": {"line": 222, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 222, "column": 24}, "end": {"line": 222, "column": 55}}, {"start": {"line": 222, "column": 59}, "end": {"line": 222, "column": 60}}]}, "11": {"loc": {"start": {"line": 223, "column": 18}, "end": {"line": 223, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 223, "column": 18}, "end": {"line": 223, "column": 43}}, {"start": {"line": 223, "column": 47}, "end": {"line": 223, "column": 48}}]}, "12": {"loc": {"start": {"line": 224, "column": 24}, "end": {"line": 224, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 224, "column": 24}, "end": {"line": 224, "column": 55}}, {"start": {"line": 224, "column": 59}, "end": {"line": 224, "column": 60}}]}, "13": {"loc": {"start": {"line": 225, "column": 25}, "end": {"line": 225, "column": 62}}, "type": "binary-expr", "locations": [{"start": {"line": 225, "column": 25}, "end": {"line": 225, "column": 57}}, {"start": {"line": 225, "column": 61}, "end": {"line": 225, "column": 62}}]}, "14": {"loc": {"start": {"line": 270, "column": 12}, "end": {"line": 270, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 270, "column": 12}, "end": {"line": 270, "column": 28}}, {"start": {"line": 270, "column": 32}, "end": {"line": 270, "column": 40}}]}, "15": {"loc": {"start": {"line": 273, "column": 17}, "end": {"line": 273, "column": 72}}, "type": "binary-expr", "locations": [{"start": {"line": 273, "column": 17}, "end": {"line": 273, "column": 30}}, {"start": {"line": 273, "column": 34}, "end": {"line": 273, "column": 72}}]}, "16": {"loc": {"start": {"line": 301, "column": 29}, "end": {"line": 301, "column": 65}}, "type": "binary-expr", "locations": [{"start": {"line": 301, "column": 29}, "end": {"line": 301, "column": 60}}, {"start": {"line": 301, "column": 64}, "end": {"line": 301, "column": 65}}]}, "17": {"loc": {"start": {"line": 302, "column": 31}, "end": {"line": 302, "column": 69}}, "type": "binary-expr", "locations": [{"start": {"line": 302, "column": 31}, "end": {"line": 302, "column": 64}}, {"start": {"line": 302, "column": 68}, "end": {"line": 302, "column": 69}}]}, "18": {"loc": {"start": {"line": 303, "column": 33}, "end": {"line": 303, "column": 78}}, "type": "binary-expr", "locations": [{"start": {"line": 303, "column": 33}, "end": {"line": 303, "column": 71}}, {"start": {"line": 303, "column": 75}, "end": {"line": 303, "column": 78}}]}, "19": {"loc": {"start": {"line": 309, "column": 21}, "end": {"line": 310, "column": 79}}, "type": "cond-expr", "locations": [{"start": {"line": 310, "column": 8}, "end": {"line": 310, "column": 75}}, {"start": {"line": 310, "column": 78}, "end": {"line": 310, "column": 79}}]}, "20": {"loc": {"start": {"line": 347, "column": 33}, "end": {"line": 347, "column": 87}}, "type": "binary-expr", "locations": [{"start": {"line": 347, "column": 33}, "end": {"line": 347, "column": 80}}, {"start": {"line": 347, "column": 84}, "end": {"line": 347, "column": 87}}]}, "21": {"loc": {"start": {"line": 348, "column": 30}, "end": {"line": 348, "column": 69}}, "type": "binary-expr", "locations": [{"start": {"line": 348, "column": 30}, "end": {"line": 348, "column": 63}}, {"start": {"line": 348, "column": 67}, "end": {"line": 348, "column": 69}}]}, "22": {"loc": {"start": {"line": 349, "column": 32}, "end": {"line": 349, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 349, "column": 32}, "end": {"line": 349, "column": 66}}, {"start": {"line": 349, "column": 70}, "end": {"line": 349, "column": 71}}]}, "23": {"loc": {"start": {"line": 350, "column": 27}, "end": {"line": 350, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 350, "column": 27}, "end": {"line": 350, "column": 56}}, {"start": {"line": 350, "column": 60}, "end": {"line": 350, "column": 61}}]}, "24": {"loc": {"start": {"line": 351, "column": 19}, "end": {"line": 351, "column": 91}}, "type": "cond-expr", "locations": [{"start": {"line": 351, "column": 41}, "end": {"line": 351, "column": 83}}, {"start": {"line": 351, "column": 87}, "end": {"line": 351, "column": 91}}]}, "25": {"loc": {"start": {"line": 403, "column": 4}, "end": {"line": 410, "column": 5}}, "type": "if", "locations": [{"start": {"line": 403, "column": 4}, "end": {"line": 410, "column": 5}}]}, "26": {"loc": {"start": {"line": 403, "column": 8}, "end": {"line": 403, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 403, "column": 8}, "end": {"line": 403, "column": 30}}, {"start": {"line": 403, "column": 34}, "end": {"line": 403, "column": 61}}]}, "27": {"loc": {"start": {"line": 413, "column": 24}, "end": {"line": 413, "column": 68}}, "type": "binary-expr", "locations": [{"start": {"line": 413, "column": 24}, "end": {"line": 413, "column": 63}}, {"start": {"line": 413, "column": 67}, "end": {"line": 413, "column": 68}}]}, "28": {"loc": {"start": {"line": 414, "column": 25}, "end": {"line": 414, "column": 70}}, "type": "binary-expr", "locations": [{"start": {"line": 414, "column": 25}, "end": {"line": 414, "column": 65}}, {"start": {"line": 414, "column": 69}, "end": {"line": 414, "column": 70}}]}, "29": {"loc": {"start": {"line": 415, "column": 4}, "end": {"line": 423, "column": 5}}, "type": "if", "locations": [{"start": {"line": 415, "column": 4}, "end": {"line": 423, "column": 5}}]}, "30": {"loc": {"start": {"line": 426, "column": 25}, "end": {"line": 426, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 426, "column": 25}, "end": {"line": 426, "column": 58}}, {"start": {"line": 426, "column": 62}, "end": {"line": 426, "column": 63}}]}, "31": {"loc": {"start": {"line": 427, "column": 4}, "end": {"line": 434, "column": 5}}, "type": "if", "locations": [{"start": {"line": 427, "column": 4}, "end": {"line": 434, "column": 5}}]}, "32": {"loc": {"start": {"line": 437, "column": 4}, "end": {"line": 458, "column": 5}}, "type": "if", "locations": [{"start": {"line": 437, "column": 4}, "end": {"line": 458, "column": 5}}]}, "33": {"loc": {"start": {"line": 502, "column": 14}, "end": {"line": 505, "column": 67}}, "type": "cond-expr", "locations": [{"start": {"line": 502, "column": 41}, "end": {"line": 502, "column": 64}}, {"start": {"line": 503, "column": 14}, "end": {"line": 505, "column": 67}}]}, "34": {"loc": {"start": {"line": 503, "column": 14}, "end": {"line": 505, "column": 67}}, "type": "cond-expr", "locations": [{"start": {"line": 503, "column": 46}, "end": {"line": 503, "column": 70}}, {"start": {"line": 504, "column": 14}, "end": {"line": 505, "column": 67}}]}, "35": {"loc": {"start": {"line": 504, "column": 14}, "end": {"line": 505, "column": 67}}, "type": "cond-expr", "locations": [{"start": {"line": 504, "column": 42}, "end": {"line": 504, "column": 53}}, {"start": {"line": 505, "column": 14}, "end": {"line": 505, "column": 67}}]}, "36": {"loc": {"start": {"line": 505, "column": 14}, "end": {"line": 505, "column": 67}}, "type": "cond-expr", "locations": [{"start": {"line": 505, "column": 42}, "end": {"line": 505, "column": 52}}, {"start": {"line": 505, "column": 55}, "end": {"line": 505, "column": 67}}]}, "37": {"loc": {"start": {"line": 506, "column": 17}, "end": {"line": 506, "column": 86}}, "type": "cond-expr", "locations": [{"start": {"line": 506, "column": 39}, "end": {"line": 506, "column": 44}}, {"start": {"line": 506, "column": 47}, "end": {"line": 506, "column": 86}}]}, "38": {"loc": {"start": {"line": 506, "column": 47}, "end": {"line": 506, "column": 86}}, "type": "cond-expr", "locations": [{"start": {"line": 506, "column": 69}, "end": {"line": 506, "column": 77}}, {"start": {"line": 506, "column": 80}, "end": {"line": 506, "column": 86}}]}, "39": {"loc": {"start": {"line": 507, "column": 21}, "end": {"line": 509, "column": 36}}, "type": "cond-expr", "locations": [{"start": {"line": 507, "column": 78}, "end": {"line": 507, "column": 89}}, {"start": {"line": 508, "column": 21}, "end": {"line": 509, "column": 36}}]}, "40": {"loc": {"start": {"line": 507, "column": 21}, "end": {"line": 507, "column": 75}}, "type": "binary-expr", "locations": [{"start": {"line": 507, "column": 21}, "end": {"line": 507, "column": 46}}, {"start": {"line": 507, "column": 50}, "end": {"line": 507, "column": 75}}]}, "41": {"loc": {"start": {"line": 508, "column": 21}, "end": {"line": 509, "column": 36}}, "type": "cond-expr", "locations": [{"start": {"line": 508, "column": 53}, "end": {"line": 508, "column": 68}}, {"start": {"line": 509, "column": 21}, "end": {"line": 509, "column": 36}}]}, "42": {"loc": {"start": {"line": 512, "column": 13}, "end": {"line": 512, "column": 28}}, "type": "binary-expr", "locations": [{"start": {"line": 512, "column": 13}, "end": {"line": 512, "column": 22}}, {"start": {"line": 512, "column": 26}, "end": {"line": 512, "column": 28}}]}, "43": {"loc": {"start": {"line": 562, "column": 13}, "end": {"line": 562, "column": 38}}, "type": "binary-expr", "locations": [{"start": {"line": 562, "column": 13}, "end": {"line": 562, "column": 32}}, {"start": {"line": 562, "column": 36}, "end": {"line": 562, "column": 38}}]}, "44": {"loc": {"start": {"line": 563, "column": 18}, "end": {"line": 563, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 563, "column": 18}, "end": {"line": 563, "column": 32}}, {"start": {"line": 563, "column": 36}, "end": {"line": 563, "column": 55}}]}, "45": {"loc": {"start": {"line": 607, "column": 29}, "end": {"line": 607, "column": 65}}, "type": "binary-expr", "locations": [{"start": {"line": 607, "column": 29}, "end": {"line": 607, "column": 60}}, {"start": {"line": 607, "column": 64}, "end": {"line": 607, "column": 65}}]}, "46": {"loc": {"start": {"line": 609, "column": 23}, "end": {"line": 611, "column": 41}}, "type": "cond-expr", "locations": [{"start": {"line": 610, "column": 8}, "end": {"line": 610, "column": 76}}, {"start": {"line": 611, "column": 8}, "end": {"line": 611, "column": 41}}]}, "47": {"loc": {"start": {"line": 611, "column": 8}, "end": {"line": 611, "column": 41}}, "type": "cond-expr", "locations": [{"start": {"line": 611, "column": 35}, "end": {"line": 611, "column": 37}}, {"start": {"line": 611, "column": 40}, "end": {"line": 611, "column": 41}}]}, "48": {"loc": {"start": {"line": 617, "column": 16}, "end": {"line": 618, "column": 62}}, "type": "cond-expr", "locations": [{"start": {"line": 617, "column": 43}, "end": {"line": 617, "column": 52}}, {"start": {"line": 618, "column": 16}, "end": {"line": 618, "column": 62}}]}, "49": {"loc": {"start": {"line": 618, "column": 16}, "end": {"line": 618, "column": 62}}, "type": "cond-expr", "locations": [{"start": {"line": 618, "column": 43}, "end": {"line": 618, "column": 51}}, {"start": {"line": 618, "column": 54}, "end": {"line": 618, "column": 62}}]}, "50": {"loc": {"start": {"line": 620, "column": 21}, "end": {"line": 624, "column": 56}}, "type": "cond-expr", "locations": [{"start": {"line": 621, "column": 11}, "end": {"line": 623, "column": 58}}, {"start": {"line": 624, "column": 10}, "end": {"line": 624, "column": 56}}]}, "51": {"loc": {"start": {"line": 621, "column": 11}, "end": {"line": 623, "column": 58}}, "type": "cond-expr", "locations": [{"start": {"line": 621, "column": 27}, "end": {"line": 621, "column": 44}}, {"start": {"line": 622, "column": 11}, "end": {"line": 623, "column": 58}}]}, "52": {"loc": {"start": {"line": 622, "column": 11}, "end": {"line": 623, "column": 58}}, "type": "cond-expr", "locations": [{"start": {"line": 622, "column": 27}, "end": {"line": 622, "column": 51}}, {"start": {"line": 623, "column": 11}, "end": {"line": 623, "column": 58}}]}, "53": {"loc": {"start": {"line": 623, "column": 11}, "end": {"line": 623, "column": 58}}, "type": "cond-expr", "locations": [{"start": {"line": 623, "column": 27}, "end": {"line": 623, "column": 37}}, {"start": {"line": 623, "column": 40}, "end": {"line": 623, "column": 58}}]}, "54": {"loc": {"start": {"line": 624, "column": 10}, "end": {"line": 624, "column": 56}}, "type": "cond-expr", "locations": [{"start": {"line": 624, "column": 37}, "end": {"line": 624, "column": 45}}, {"start": {"line": 624, "column": 48}, "end": {"line": 624, "column": 56}}]}, "55": {"loc": {"start": {"line": 627, "column": 29}, "end": {"line": 629, "column": 64}}, "type": "cond-expr", "locations": [{"start": {"line": 628, "column": 10}, "end": {"line": 628, "column": 89}}, {"start": {"line": 629, "column": 10}, "end": {"line": 629, "column": 64}}]}, "56": {"loc": {"start": {"line": 629, "column": 10}, "end": {"line": 629, "column": 64}}, "type": "cond-expr", "locations": [{"start": {"line": 629, "column": 37}, "end": {"line": 629, "column": 45}}, {"start": {"line": 629, "column": 48}, "end": {"line": 629, "column": 64}}]}, "57": {"loc": {"start": {"line": 659, "column": 10}, "end": {"line": 659, "column": 76}}, "type": "binary-expr", "locations": [{"start": {"line": 659, "column": 10}, "end": {"line": 659, "column": 40}}, {"start": {"line": 659, "column": 44}, "end": {"line": 659, "column": 76}}]}, "58": {"loc": {"start": {"line": 660, "column": 12}, "end": {"line": 660, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 660, "column": 12}, "end": {"line": 660, "column": 20}}, {"start": {"line": 660, "column": 24}, "end": {"line": 660, "column": 48}}]}, "59": {"loc": {"start": {"line": 692, "column": 16}, "end": {"line": 692, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 692, "column": 16}, "end": {"line": 692, "column": 28}}, {"start": {"line": 692, "column": 32}, "end": {"line": 692, "column": 50}}]}}, "s": {"0": 4, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 4, "100": 4, "101": 4, "102": 4, "103": 4, "104": 4, "105": 4, "106": 4, "107": 4, "108": 4, "109": 4, "110": 4, "111": 4, "112": 4, "113": 4, "114": 4, "115": 4, "116": 4}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0, 0], "4": [0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0], "26": [0, 0], "27": [0, 0], "28": [0, 0], "29": [0], "30": [0, 0], "31": [0], "32": [0], "33": [0, 0], "34": [0, 0], "35": [0, 0], "36": [0, 0], "37": [0, 0], "38": [0, 0], "39": [0, 0], "40": [0, 0], "41": [0, 0], "42": [0, 0], "43": [0, 0], "44": [0, 0], "45": [0, 0], "46": [0, 0], "47": [0, 0], "48": [0, 0], "49": [0, 0], "50": [0, 0], "51": [0, 0], "52": [0, 0], "53": [0, 0], "54": [0, 0], "55": [0, 0], "56": [0, 0], "57": [0, 0], "58": [0, 0], "59": [0, 0]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/database/index.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/database/index.ts", "statementMap": {"0": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 38}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 4}, "f": {}, "b": {}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/hooks/use-toast.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/hooks/use-toast.ts", "statementMap": {"0": {"start": {"line": 191, "column": 9}, "end": {"line": 191, "column": 17}}, "1": {"start": {"line": 191, "column": 19}, "end": {"line": 191, "column": 24}}, "2": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 30}}, "3": {"start": {"line": 8, "column": 20}, "end": {"line": 8, "column": 21}}, "4": {"start": {"line": 9, "column": 27}, "end": {"line": 9, "column": 34}}, "5": {"start": {"line": 18, "column": 20}, "end": {"line": 23, "column": null}}, "6": {"start": {"line": 25, "column": 12}, "end": {"line": 25, "column": 13}}, "7": {"start": {"line": 28, "column": 2}, "end": {"line": 28, "column": null}}, "8": {"start": {"line": 29, "column": 2}, "end": {"line": 29, "column": null}}, "9": {"start": {"line": 56, "column": 22}, "end": {"line": 56, "column": 70}}, "10": {"start": {"line": 58, "column": 25}, "end": {"line": 72, "column": 1}}, "11": {"start": {"line": 59, "column": 2}, "end": {"line": 61, "column": 3}}, "12": {"start": {"line": 60, "column": 4}, "end": {"line": 60, "column": 10}}, "13": {"start": {"line": 63, "column": 18}, "end": {"line": 69, "column": 24}}, "14": {"start": {"line": 64, "column": 4}, "end": {"line": 64, "column": null}}, "15": {"start": {"line": 65, "column": 4}, "end": {"line": 68, "column": null}}, "16": {"start": {"line": 71, "column": 2}, "end": {"line": 71, "column": null}}, "17": {"start": {"line": 74, "column": 23}, "end": {"line": 127, "column": 1}}, "18": {"start": {"line": 75, "column": 2}, "end": {"line": 126, "column": 3}}, "19": {"start": {"line": 77, "column": 6}, "end": {"line": 80, "column": null}}, "20": {"start": {"line": 83, "column": 6}, "end": {"line": 88, "column": null}}, "21": {"start": {"line": 86, "column": 10}, "end": {"line": 86, "column": 66}}, "22": {"start": {"line": 91, "column": 26}, "end": {"line": 91, "column": 32}}, "23": {"start": {"line": 95, "column": 6}, "end": {"line": 101, "column": 7}}, "24": {"start": {"line": 96, "column": 8}, "end": {"line": 96, "column": null}}, "25": {"start": {"line": 98, "column": 8}, "end": {"line": 100, "column": null}}, "26": {"start": {"line": 99, "column": 10}, "end": {"line": 99, "column": null}}, "27": {"start": {"line": 103, "column": 6}, "end": {"line": 113, "column": null}}, "28": {"start": {"line": 106, "column": 10}, "end": {"line": 111, "column": 15}}, "29": {"start": {"line": 116, "column": 6}, "end": {"line": 121, "column": 7}}, "30": {"start": {"line": 117, "column": 8}, "end": {"line": 120, "column": null}}, "31": {"start": {"line": 122, "column": 6}, "end": {"line": 125, "column": null}}, "32": {"start": {"line": 124, "column": 43}, "end": {"line": 124, "column": 66}}, "33": {"start": {"line": 74, "column": 13}, "end": {"line": 74, "column": 23}}, "34": {"start": {"line": 129, "column": 49}, "end": {"line": 129, "column": 51}}, "35": {"start": {"line": 131, "column": 25}, "end": {"line": 131, "column": 39}}, "36": {"start": {"line": 134, "column": 2}, "end": {"line": 134, "column": null}}, "37": {"start": {"line": 135, "column": 2}, "end": {"line": 137, "column": null}}, "38": {"start": {"line": 136, "column": 4}, "end": {"line": 136, "column": null}}, "39": {"start": {"line": 143, "column": 13}, "end": {"line": 143, "column": 20}}, "40": {"start": {"line": 145, "column": 17}, "end": {"line": 149, "column": 6}}, "41": {"start": {"line": 146, "column": 4}, "end": {"line": 149, "column": 6}}, "42": {"start": {"line": 150, "column": 18}, "end": {"line": 150, "column": 72}}, "43": {"start": {"line": 150, "column": 24}, "end": {"line": 150, "column": 72}}, "44": {"start": {"line": 152, "column": 2}, "end": {"line": 162, "column": null}}, "45": {"start": {"line": 159, "column": 8}, "end": {"line": 159, "column": null}}, "46": {"start": {"line": 159, "column": 19}, "end": {"line": 159, "column": null}}, "47": {"start": {"line": 164, "column": 2}, "end": {"line": 168, "column": null}}, "48": {"start": {"line": 172, "column": 28}, "end": {"line": 172, "column": 62}}, "49": {"start": {"line": 174, "column": 2}, "end": {"line": 182, "column": null}}, "50": {"start": {"line": 175, "column": 4}, "end": {"line": 175, "column": null}}, "51": {"start": {"line": 176, "column": 4}, "end": {"line": 181, "column": null}}, "52": {"start": {"line": 177, "column": 20}, "end": {"line": 177, "column": 47}}, "53": {"start": {"line": 178, "column": 6}, "end": {"line": 180, "column": 7}}, "54": {"start": {"line": 179, "column": 8}, "end": {"line": 179, "column": null}}, "55": {"start": {"line": 184, "column": 2}, "end": {"line": 188, "column": null}}, "56": {"start": {"line": 187, "column": 35}, "end": {"line": 187, "column": 79}}}, "fnMap": {"0": {"name": "genId", "decl": {"start": {"line": 27, "column": 9}, "end": {"line": 27, "column": 14}}, "loc": {"start": {"line": 27, "column": 14}, "end": {"line": 30, "column": 1}}}, "1": {"name": "(anonymous_10)", "decl": {"start": {"line": 58, "column": 25}, "end": {"line": 58, "column": 26}}, "loc": {"start": {"line": 58, "column": 45}, "end": {"line": 72, "column": 1}}}, "2": {"name": "(anonymous_11)", "decl": {"start": {"line": 63, "column": 29}, "end": {"line": 63, "column": 32}}, "loc": {"start": {"line": 63, "column": 34}, "end": {"line": 69, "column": 3}}}, "3": {"name": "(anonymous_12)", "decl": {"start": {"line": 74, "column": 23}, "end": {"line": 74, "column": 24}}, "loc": {"start": {"line": 74, "column": 63}, "end": {"line": 127, "column": 1}}}, "4": {"name": "(anonymous_13)", "decl": {"start": {"line": 85, "column": 33}, "end": {"line": 85, "column": 34}}, "loc": {"start": {"line": 86, "column": 10}, "end": {"line": 86, "column": 66}}}, "5": {"name": "(anonymous_14)", "decl": {"start": {"line": 98, "column": 29}, "end": {"line": 98, "column": 30}}, "loc": {"start": {"line": 98, "column": 39}, "end": {"line": 100, "column": 9}}}, "6": {"name": "(anonymous_15)", "decl": {"start": {"line": 105, "column": 33}, "end": {"line": 105, "column": 34}}, "loc": {"start": {"line": 106, "column": 10}, "end": {"line": 111, "column": 15}}}, "7": {"name": "(anonymous_16)", "decl": {"start": {"line": 124, "column": 36}, "end": {"line": 124, "column": 37}}, "loc": {"start": {"line": 124, "column": 43}, "end": {"line": 124, "column": 66}}}, "8": {"name": "dispatch", "decl": {"start": {"line": 133, "column": 9}, "end": {"line": 133, "column": 17}}, "loc": {"start": {"line": 133, "column": 32}, "end": {"line": 138, "column": 1}}}, "9": {"name": "(anonymous_18)", "decl": {"start": {"line": 135, "column": 20}, "end": {"line": 135, "column": 21}}, "loc": {"start": {"line": 135, "column": 33}, "end": {"line": 137, "column": 3}}}, "10": {"name": "toast", "decl": {"start": {"line": 142, "column": 9}, "end": {"line": 142, "column": 14}}, "loc": {"start": {"line": 142, "column": 34}, "end": {"line": 169, "column": 1}}}, "11": {"name": "(anonymous_20)", "decl": {"start": {"line": 145, "column": 17}, "end": {"line": 145, "column": 18}}, "loc": {"start": {"line": 146, "column": 4}, "end": {"line": 149, "column": 6}}}, "12": {"name": "(anonymous_21)", "decl": {"start": {"line": 150, "column": 18}, "end": {"line": 150, "column": 21}}, "loc": {"start": {"line": 150, "column": 24}, "end": {"line": 150, "column": 72}}}, "13": {"name": "(anonymous_22)", "decl": {"start": {"line": 158, "column": 20}, "end": {"line": 158, "column": 21}}, "loc": {"start": {"line": 158, "column": 29}, "end": {"line": 160, "column": 7}}}, "14": {"name": "useToast", "decl": {"start": {"line": 171, "column": 9}, "end": {"line": 171, "column": 17}}, "loc": {"start": {"line": 171, "column": 17}, "end": {"line": 189, "column": 1}}}, "15": {"name": "(anonymous_24)", "decl": {"start": {"line": 174, "column": 18}, "end": {"line": 174, "column": 21}}, "loc": {"start": {"line": 174, "column": 23}, "end": {"line": 182, "column": 3}}}, "16": {"name": "(anonymous_25)", "decl": {"start": {"line": 176, "column": 11}, "end": {"line": 176, "column": 14}}, "loc": {"start": {"line": 176, "column": 16}, "end": {"line": 181, "column": 5}}}, "17": {"name": "(anonymous_26)", "decl": {"start": {"line": 187, "column": 13}, "end": {"line": 187, "column": 14}}, "loc": {"start": {"line": 187, "column": 35}, "end": {"line": 187, "column": 79}}}}, "branchMap": {"0": {"loc": {"start": {"line": 59, "column": 2}, "end": {"line": 61, "column": 3}}, "type": "if", "locations": [{"start": {"line": 59, "column": 2}, "end": {"line": 61, "column": 3}}]}, "1": {"loc": {"start": {"line": 75, "column": 2}, "end": {"line": 126, "column": 3}}, "type": "switch", "locations": [{"start": {"line": 76, "column": 4}, "end": {"line": 80, "column": null}}, {"start": {"line": 82, "column": 4}, "end": {"line": 88, "column": null}}, {"start": {"line": 90, "column": 4}, "end": {"line": 114, "column": 5}}, {"start": {"line": 115, "column": 4}, "end": {"line": 125, "column": null}}]}, "2": {"loc": {"start": {"line": 86, "column": 10}, "end": {"line": 86, "column": 66}}, "type": "cond-expr", "locations": [{"start": {"line": 86, "column": 37}, "end": {"line": 86, "column": 62}}, {"start": {"line": 86, "column": 65}, "end": {"line": 86, "column": 66}}]}, "3": {"loc": {"start": {"line": 95, "column": 6}, "end": {"line": 101, "column": 7}}, "type": "if", "locations": [{"start": {"line": 95, "column": 6}, "end": {"line": 101, "column": 7}}, {"start": {"line": 97, "column": 13}, "end": {"line": 101, "column": 7}}]}, "4": {"loc": {"start": {"line": 106, "column": 10}, "end": {"line": 111, "column": 15}}, "type": "cond-expr", "locations": [{"start": {"line": 107, "column": 14}, "end": {"line": 110, "column": null}}, {"start": {"line": 111, "column": 14}, "end": {"line": 111, "column": 15}}]}, "5": {"loc": {"start": {"line": 106, "column": 10}, "end": {"line": 106, "column": 51}}, "type": "binary-expr", "locations": [{"start": {"line": 106, "column": 10}, "end": {"line": 106, "column": 26}}, {"start": {"line": 106, "column": 30}, "end": {"line": 106, "column": 51}}]}, "6": {"loc": {"start": {"line": 116, "column": 6}, "end": {"line": 121, "column": 7}}, "type": "if", "locations": [{"start": {"line": 116, "column": 6}, "end": {"line": 121, "column": 7}}]}, "7": {"loc": {"start": {"line": 159, "column": 8}, "end": {"line": 159, "column": null}}, "type": "if", "locations": [{"start": {"line": 159, "column": 8}, "end": {"line": 159, "column": null}}]}, "8": {"loc": {"start": {"line": 178, "column": 6}, "end": {"line": 180, "column": 7}}, "type": "if", "locations": [{"start": {"line": 178, "column": 6}, "end": {"line": 180, "column": 7}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0}, "b": {"0": [0], "1": [0, 0, 0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0], "7": [0], "8": [0]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/hooks/useComplianceApi.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/hooks/useComplianceApi.ts", "statementMap": {"0": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 78}}, "1": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 77}}, "2": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 31}}, "3": {"start": {"line": 7, "column": 33}, "end": {"line": 20, "column": 1}}, "4": {"start": {"line": 8, "column": 22}, "end": {"line": 8, "column": 38}}, "5": {"start": {"line": 10, "column": 2}, "end": {"line": 19, "column": 5}}, "6": {"start": {"line": 13, "column": 6}, "end": {"line": 13, "column": 63}}, "7": {"start": {"line": 14, "column": 6}, "end": {"line": 14, "column": 65}}, "8": {"start": {"line": 17, "column": 6}, "end": {"line": 17, "column": 65}}, "9": {"start": {"line": 7, "column": 13}, "end": {"line": 7, "column": 33}}, "10": {"start": {"line": 23, "column": 37}, "end": {"line": 37, "column": 1}}, "11": {"start": {"line": 24, "column": 22}, "end": {"line": 24, "column": 38}}, "12": {"start": {"line": 26, "column": 2}, "end": {"line": 36, "column": 5}}, "13": {"start": {"line": 28, "column": 6}, "end": {"line": 28, "column": 58}}, "14": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 76}}, "15": {"start": {"line": 31, "column": 6}, "end": {"line": 31, "column": 65}}, "16": {"start": {"line": 34, "column": 6}, "end": {"line": 34, "column": 65}}, "17": {"start": {"line": 23, "column": 13}, "end": {"line": 23, "column": 37}}, "18": {"start": {"line": 40, "column": 28}, "end": {"line": 53, "column": 1}}, "19": {"start": {"line": 41, "column": 22}, "end": {"line": 41, "column": 38}}, "20": {"start": {"line": 43, "column": 2}, "end": {"line": 52, "column": 5}}, "21": {"start": {"line": 46, "column": 6}, "end": {"line": 46, "column": 48}}, "22": {"start": {"line": 47, "column": 6}, "end": {"line": 47, "column": 59}}, "23": {"start": {"line": 50, "column": 6}, "end": {"line": 50, "column": 60}}, "24": {"start": {"line": 40, "column": 13}, "end": {"line": 40, "column": 28}}, "25": {"start": {"line": 56, "column": 37}, "end": {"line": 70, "column": 1}}, "26": {"start": {"line": 57, "column": 22}, "end": {"line": 57, "column": 38}}, "27": {"start": {"line": 59, "column": 2}, "end": {"line": 69, "column": 5}}, "28": {"start": {"line": 61, "column": 6}, "end": {"line": 61, "column": 61}}, "29": {"start": {"line": 63, "column": 6}, "end": {"line": 63, "column": 59}}, "30": {"start": {"line": 64, "column": 6}, "end": {"line": 64, "column": 59}}, "31": {"start": {"line": 67, "column": 6}, "end": {"line": 67, "column": 70}}, "32": {"start": {"line": 56, "column": 13}, "end": {"line": 56, "column": 37}}, "33": {"start": {"line": 73, "column": 33}, "end": {"line": 86, "column": 1}}, "34": {"start": {"line": 74, "column": 22}, "end": {"line": 74, "column": 38}}, "35": {"start": {"line": 76, "column": 2}, "end": {"line": 85, "column": 5}}, "36": {"start": {"line": 79, "column": 6}, "end": {"line": 79, "column": 49}}, "37": {"start": {"line": 80, "column": 6}, "end": {"line": 80, "column": 63}}, "38": {"start": {"line": 83, "column": 6}, "end": {"line": 83, "column": 65}}, "39": {"start": {"line": 73, "column": 13}, "end": {"line": 73, "column": 33}}, "40": {"start": {"line": 89, "column": 42}, "end": {"line": 102, "column": 1}}, "41": {"start": {"line": 90, "column": 22}, "end": {"line": 90, "column": 38}}, "42": {"start": {"line": 92, "column": 2}, "end": {"line": 101, "column": 5}}, "43": {"start": {"line": 95, "column": 6}, "end": {"line": 95, "column": 58}}, "44": {"start": {"line": 96, "column": 6}, "end": {"line": 96, "column": 66}}, "45": {"start": {"line": 99, "column": 6}, "end": {"line": 99, "column": 71}}, "46": {"start": {"line": 89, "column": 13}, "end": {"line": 89, "column": 42}}, "47": {"start": {"line": 105, "column": 33}, "end": {"line": 148, "column": 1}}, "48": {"start": {"line": 106, "column": 2}, "end": {"line": 147, "column": 5}}, "49": {"start": {"line": 109, "column": 6}, "end": {"line": 109, "column": 35}}, "50": {"start": {"line": 109, "column": 23}, "end": {"line": 109, "column": 35}}, "51": {"start": {"line": 111, "column": 6}, "end": {"line": 111, "column": 68}}, "52": {"start": {"line": 112, "column": 21}, "end": {"line": 112, "column": 70}}, "53": {"start": {"line": 113, "column": 6}, "end": {"line": 113, "column": 73}}, "54": {"start": {"line": 115, "column": 6}, "end": {"line": 115, "column": 20}}, "55": {"start": {"line": 120, "column": 6}, "end": {"line": 123, "column": 7}}, "56": {"start": {"line": 121, "column": 8}, "end": {"line": 121, "column": 69}}, "57": {"start": {"line": 122, "column": 8}, "end": {"line": 122, "column": 21}}, "58": {"start": {"line": 126, "column": 27}, "end": {"line": 126, "column": 43}}, "59": {"start": {"line": 127, "column": 28}, "end": {"line": 127, "column": 48}}, "60": {"start": {"line": 130, "column": 6}, "end": {"line": 133, "column": 7}}, "61": {"start": {"line": 131, "column": 8}, "end": {"line": 131, "column": 77}}, "62": {"start": {"line": 132, "column": 8}, "end": {"line": 132, "column": 21}}, "63": {"start": {"line": 135, "column": 6}, "end": {"line": 138, "column": 7}}, "64": {"start": {"line": 136, "column": 8}, "end": {"line": 136, "column": 60}}, "65": {"start": {"line": 137, "column": 8}, "end": {"line": 137, "column": 21}}, "66": {"start": {"line": 141, "column": 6}, "end": {"line": 141, "column": 82}}, "67": {"start": {"line": 142, "column": 6}, "end": {"line": 142, "column": 18}}, "68": {"start": {"line": 105, "column": 13}, "end": {"line": 105, "column": 33}}, "69": {"start": {"line": 151, "column": 33}, "end": {"line": 157, "column": 1}}, "70": {"start": {"line": 152, "column": 2}, "end": {"line": 156, "column": 5}}, "71": {"start": {"line": 154, "column": 19}, "end": {"line": 154, "column": 82}}, "72": {"start": {"line": 151, "column": 13}, "end": {"line": 151, "column": 33}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 7, "column": 33}, "end": {"line": 7, "column": 36}}, "loc": {"start": {"line": 7, "column": 38}, "end": {"line": 20, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 12, "column": 15}, "end": {"line": 12, "column": 18}}, "loc": {"start": {"line": 12, "column": 20}, "end": {"line": 15, "column": 5}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 16, "column": 13}, "end": {"line": 16, "column": 14}}, "loc": {"start": {"line": 16, "column": 23}, "end": {"line": 18, "column": 5}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 23, "column": 37}, "end": {"line": 23, "column": 40}}, "loc": {"start": {"line": 23, "column": 42}, "end": {"line": 37, "column": 1}}}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 27, "column": 16}, "end": {"line": 27, "column": 17}}, "loc": {"start": {"line": 28, "column": 6}, "end": {"line": 28, "column": 58}}}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 29, "column": 15}, "end": {"line": 29, "column": 18}}, "loc": {"start": {"line": 29, "column": 20}, "end": {"line": 32, "column": 5}}}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 33, "column": 13}, "end": {"line": 33, "column": 14}}, "loc": {"start": {"line": 33, "column": 23}, "end": {"line": 35, "column": 5}}}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 40, "column": 28}, "end": {"line": 40, "column": 31}}, "loc": {"start": {"line": 40, "column": 33}, "end": {"line": 53, "column": 1}}}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 45, "column": 15}, "end": {"line": 45, "column": 18}}, "loc": {"start": {"line": 45, "column": 20}, "end": {"line": 48, "column": 5}}}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 49, "column": 13}, "end": {"line": 49, "column": 14}}, "loc": {"start": {"line": 49, "column": 23}, "end": {"line": 51, "column": 5}}}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 56, "column": 37}, "end": {"line": 56, "column": 40}}, "loc": {"start": {"line": 56, "column": 42}, "end": {"line": 70, "column": 1}}}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 60, "column": 16}, "end": {"line": 60, "column": 17}}, "loc": {"start": {"line": 61, "column": 6}, "end": {"line": 61, "column": 61}}}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 62, "column": 15}, "end": {"line": 62, "column": 18}}, "loc": {"start": {"line": 62, "column": 20}, "end": {"line": 65, "column": 5}}}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 66, "column": 13}, "end": {"line": 66, "column": 14}}, "loc": {"start": {"line": 66, "column": 23}, "end": {"line": 68, "column": 5}}}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 73, "column": 33}, "end": {"line": 73, "column": 36}}, "loc": {"start": {"line": 73, "column": 38}, "end": {"line": 86, "column": 1}}}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 78, "column": 15}, "end": {"line": 78, "column": 18}}, "loc": {"start": {"line": 78, "column": 20}, "end": {"line": 81, "column": 5}}}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 82, "column": 13}, "end": {"line": 82, "column": 14}}, "loc": {"start": {"line": 82, "column": 23}, "end": {"line": 84, "column": 5}}}, "17": {"name": "(anonymous_17)", "decl": {"start": {"line": 89, "column": 42}, "end": {"line": 89, "column": 45}}, "loc": {"start": {"line": 89, "column": 47}, "end": {"line": 102, "column": 1}}}, "18": {"name": "(anonymous_18)", "decl": {"start": {"line": 94, "column": 15}, "end": {"line": 94, "column": 18}}, "loc": {"start": {"line": 94, "column": 20}, "end": {"line": 97, "column": 5}}}, "19": {"name": "(anonymous_19)", "decl": {"start": {"line": 98, "column": 13}, "end": {"line": 98, "column": 14}}, "loc": {"start": {"line": 98, "column": 23}, "end": {"line": 100, "column": 5}}}, "20": {"name": "(anonymous_20)", "decl": {"start": {"line": 105, "column": 33}, "end": {"line": 105, "column": 34}}, "loc": {"start": {"line": 105, "column": 88}, "end": {"line": 148, "column": 1}}}, "21": {"name": "(anonymous_21)", "decl": {"start": {"line": 108, "column": 13}, "end": {"line": 108, "column": 18}}, "loc": {"start": {"line": 108, "column": 24}, "end": {"line": 116, "column": 5}}}, "22": {"name": "(anonymous_22)", "decl": {"start": {"line": 118, "column": 21}, "end": {"line": 118, "column": 22}}, "loc": {"start": {"line": 118, "column": 31}, "end": {"line": 143, "column": 5}}}, "23": {"name": "(anonymous_23)", "decl": {"start": {"line": 151, "column": 33}, "end": {"line": 151, "column": 34}}, "loc": {"start": {"line": 151, "column": 88}, "end": {"line": 157, "column": 1}}}, "24": {"name": "(anonymous_24)", "decl": {"start": {"line": 154, "column": 13}, "end": {"line": 154, "column": 16}}, "loc": {"start": {"line": 154, "column": 19}, "end": {"line": 154, "column": 82}}}}, "branchMap": {"0": {"loc": {"start": {"line": 105, "column": 61}, "end": {"line": 105, "column": 84}}, "type": "default-arg", "locations": [{"start": {"line": 105, "column": 80}, "end": {"line": 105, "column": 84}}]}, "1": {"loc": {"start": {"line": 109, "column": 6}, "end": {"line": 109, "column": 35}}, "type": "if", "locations": [{"start": {"line": 109, "column": 6}, "end": {"line": 109, "column": 35}}]}, "2": {"loc": {"start": {"line": 117, "column": 13}, "end": {"line": 117, "column": 36}}, "type": "binary-expr", "locations": [{"start": {"line": 117, "column": 13}, "end": {"line": 117, "column": 20}}, {"start": {"line": 117, "column": 24}, "end": {"line": 117, "column": 36}}]}, "3": {"loc": {"start": {"line": 120, "column": 6}, "end": {"line": 123, "column": 7}}, "type": "if", "locations": [{"start": {"line": 120, "column": 6}, "end": {"line": 123, "column": 7}}]}, "4": {"loc": {"start": {"line": 130, "column": 6}, "end": {"line": 133, "column": 7}}, "type": "if", "locations": [{"start": {"line": 130, "column": 6}, "end": {"line": 133, "column": 7}}]}, "5": {"loc": {"start": {"line": 135, "column": 6}, "end": {"line": 138, "column": 7}}, "type": "if", "locations": [{"start": {"line": 135, "column": 6}, "end": {"line": 138, "column": 7}}]}, "6": {"loc": {"start": {"line": 151, "column": 61}, "end": {"line": 151, "column": 84}}, "type": "default-arg", "locations": [{"start": {"line": 151, "column": 80}, "end": {"line": 151, "column": 84}}]}, "7": {"loc": {"start": {"line": 154, "column": 19}, "end": {"line": 154, "column": 82}}, "type": "cond-expr", "locations": [{"start": {"line": 154, "column": 32}, "end": {"line": 154, "column": 75}}, {"start": {"line": 154, "column": 78}, "end": {"line": 154, "column": 82}}]}, "8": {"loc": {"start": {"line": 155, "column": 13}, "end": {"line": 155, "column": 36}}, "type": "binary-expr", "locations": [{"start": {"line": 155, "column": 13}, "end": {"line": 155, "column": 20}}, {"start": {"line": 155, "column": 24}, "end": {"line": 155, "column": 36}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0}, "b": {"0": [0], "1": [0], "2": [0, 0], "3": [0], "4": [0], "5": [0], "6": [0], "7": [0, 0], "8": [0, 0]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/lib/utils.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/lib/utils.ts", "statementMap": {"0": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 16}}, "1": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 44}}, "2": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 40}}, "3": {"start": {"line": 5, "column": 2}, "end": {"line": 5, "column": null}}}, "fnMap": {"0": {"name": "cn", "decl": {"start": {"line": 4, "column": 16}, "end": {"line": 4, "column": 18}}, "loc": {"start": {"line": 4, "column": 42}, "end": {"line": 6, "column": 1}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {"0": 0}, "b": {}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/middleware/cors.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/middleware/cors.ts", "statementMap": {"0": {"start": {"line": 4, "column": 30}, "end": {"line": 14, "column": 1}}, "1": {"start": {"line": 5, "column": 2}, "end": {"line": 5, "column": 49}}, "2": {"start": {"line": 6, "column": 2}, "end": {"line": 6, "column": 80}}, "3": {"start": {"line": 7, "column": 2}, "end": {"line": 7, "column": 110}}, "4": {"start": {"line": 9, "column": 2}, "end": {"line": 13, "column": 3}}, "5": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 24}}, "6": {"start": {"line": 12, "column": 4}, "end": {"line": 12, "column": 11}}, "7": {"start": {"line": 4, "column": 13}, "end": {"line": 4, "column": 30}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 4, "column": 30}, "end": {"line": 4, "column": 31}}, "loc": {"start": {"line": 4, "column": 82}, "end": {"line": 14, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 9, "column": 2}, "end": {"line": 13, "column": 3}}, "type": "if", "locations": [{"start": {"line": 9, "column": 2}, "end": {"line": 13, "column": 3}}, {"start": {"line": 11, "column": 9}, "end": {"line": 13, "column": 3}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "f": {"0": 0}, "b": {"0": [0, 0]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/middleware/error-handler.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/middleware/error-handler.ts", "statementMap": {"0": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 42}}, "1": {"start": {"line": 4, "column": 28}, "end": {"line": 15, "column": 1}}, "2": {"start": {"line": 5, "column": 2}, "end": {"line": 5, "column": 76}}, "3": {"start": {"line": 7, "column": 2}, "end": {"line": 9, "column": 3}}, "4": {"start": {"line": 8, "column": 4}, "end": {"line": 8, "column": 23}}, "5": {"start": {"line": 11, "column": 2}, "end": {"line": 14, "column": 5}}, "6": {"start": {"line": 4, "column": 13}, "end": {"line": 4, "column": 28}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 4, "column": 28}, "end": {"line": 4, "column": 29}}, "loc": {"start": {"line": 4, "column": 94}, "end": {"line": 15, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 7, "column": 2}, "end": {"line": 9, "column": 3}}, "type": "if", "locations": [{"start": {"line": 7, "column": 2}, "end": {"line": 9, "column": 3}}]}, "1": {"loc": {"start": {"line": 13, "column": 13}, "end": {"line": 13, "column": 92}}, "type": "cond-expr", "locations": [{"start": {"line": 13, "column": 54}, "end": {"line": 13, "column": 67}}, {"start": {"line": 13, "column": 70}, "end": {"line": 13, "column": 92}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "f": {"0": 0}, "b": {"0": [0], "1": [0, 0]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/middleware/index.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/middleware/index.ts", "statementMap": {"0": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 23}}, "1": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 25}}, "2": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 32}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {}, "b": {}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/middleware/upload.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/middleware/upload.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 28}}, "1": {"start": {"line": 4, "column": 13}, "end": {"line": 25, "column": 3}}, "2": {"start": {"line": 10, "column": 25}, "end": {"line": 17, "column": 6}}, "3": {"start": {"line": 19, "column": 4}, "end": {"line": 23, "column": 5}}, "4": {"start": {"line": 20, "column": 6}, "end": {"line": 20, "column": 21}}, "5": {"start": {"line": 22, "column": 6}, "end": {"line": 22, "column": 41}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 9, "column": 14}, "end": {"line": 9, "column": 15}}, "loc": {"start": {"line": 9, "column": 32}, "end": {"line": 24, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 19, "column": 4}, "end": {"line": 23, "column": 5}}, "type": "if", "locations": [{"start": {"line": 19, "column": 4}, "end": {"line": 23, "column": 5}}, {"start": {"line": 21, "column": 11}, "end": {"line": 23, "column": 5}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "f": {"0": 0}, "b": {"0": [0, 0]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/compliance.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/compliance.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 52}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 42}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 20}}, "3": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 48}}, "4": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 51}}, "5": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 46}}, "6": {"start": {"line": 10, "column": 15}, "end": {"line": 10, "column": 23}}, "7": {"start": {"line": 13, "column": 0}, "end": {"line": 33, "column": 3}}, "8": {"start": {"line": 14, "column": 2}, "end": {"line": 32, "column": 3}}, "9": {"start": {"line": 15, "column": 41}, "end": {"line": 15, "column": 49}}, "10": {"start": {"line": 18, "column": 19}, "end": {"line": 21, "column": 41}}, "11": {"start": {"line": 24, "column": 4}, "end": {"line": 28, "column": 7}}, "12": {"start": {"line": 30, "column": 4}, "end": {"line": 30, "column": 80}}, "13": {"start": {"line": 31, "column": 4}, "end": {"line": 31, "column": 61}}, "14": {"start": {"line": 36, "column": 0}, "end": {"line": 75, "column": 3}}, "15": {"start": {"line": 37, "column": 2}, "end": {"line": 74, "column": 3}}, "16": {"start": {"line": 38, "column": 4}, "end": {"line": 41, "column": 5}}, "17": {"start": {"line": 39, "column": 6}, "end": {"line": 39, "column": 58}}, "18": {"start": {"line": 40, "column": 6}, "end": {"line": 40, "column": 13}}, "19": {"start": {"line": 43, "column": 41}, "end": {"line": 43, "column": 49}}, "20": {"start": {"line": 46, "column": 24}, "end": {"line": 46, "column": 62}}, "21": {"start": {"line": 49, "column": 41}, "end": {"line": 55, "column": 6}}, "22": {"start": {"line": 58, "column": 19}, "end": {"line": 61, "column": 41}}, "23": {"start": {"line": 64, "column": 4}, "end": {"line": 64, "column": 33}}, "24": {"start": {"line": 66, "column": 4}, "end": {"line": 70, "column": 7}}, "25": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 79}}, "26": {"start": {"line": 73, "column": 4}, "end": {"line": 73, "column": 61}}, "27": {"start": {"line": 78, "column": 0}, "end": {"line": 82, "column": 3}}, "28": {"start": {"line": 79, "column": 2}, "end": {"line": 79, "column": 48}}, "29": {"start": {"line": 81, "column": 2}, "end": {"line": 81, "column": 98}}, "30": {"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 22}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 13, "column": 25}, "end": {"line": 13, "column": 30}}, "loc": {"start": {"line": 13, "column": 78}, "end": {"line": 33, "column": 1}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 36, "column": 55}, "end": {"line": 36, "column": 60}}, "loc": {"start": {"line": 36, "column": 104}, "end": {"line": 75, "column": 1}}}, "2": {"name": "(anonymous_3)", "decl": {"start": {"line": 78, "column": 34}, "end": {"line": 78, "column": 35}}, "loc": {"start": {"line": 78, "column": 73}, "end": {"line": 82, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 38, "column": 4}, "end": {"line": 41, "column": 5}}, "type": "if", "locations": [{"start": {"line": 38, "column": 4}, "end": {"line": 41, "column": 5}}]}, "1": {"loc": {"start": {"line": 50, "column": 10}, "end": {"line": 50, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 50, "column": 10}, "end": {"line": 50, "column": 20}}, {"start": {"line": 50, "column": 24}, "end": {"line": 50, "column": 43}}]}, "2": {"loc": {"start": {"line": 52, "column": 20}, "end": {"line": 52, "column": 47}}, "type": "binary-expr", "locations": [{"start": {"line": 52, "column": 20}, "end": {"line": 52, "column": 32}}, {"start": {"line": 52, "column": 36}, "end": {"line": 52, "column": 47}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/dashboard.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/dashboard.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 52}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 49}}, "2": {"start": {"line": 4, "column": 15}, "end": {"line": 4, "column": 23}}, "3": {"start": {"line": 7, "column": 0}, "end": {"line": 19, "column": 3}}, "4": {"start": {"line": 8, "column": 2}, "end": {"line": 18, "column": 3}}, "5": {"start": {"line": 9, "column": 4}, "end": {"line": 9, "column": 50}}, "6": {"start": {"line": 12, "column": 20}, "end": {"line": 12, "column": 66}}, "7": {"start": {"line": 14, "column": 4}, "end": {"line": 14, "column": 22}}, "8": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 62}}, "9": {"start": {"line": 17, "column": 4}, "end": {"line": 17, "column": 73}}, "10": {"start": {"line": 22, "column": 0}, "end": {"line": 34, "column": 3}}, "11": {"start": {"line": 23, "column": 2}, "end": {"line": 33, "column": 3}}, "12": {"start": {"line": 24, "column": 4}, "end": {"line": 24, "column": 53}}, "13": {"start": {"line": 27, "column": 22}, "end": {"line": 27, "column": 71}}, "14": {"start": {"line": 29, "column": 4}, "end": {"line": 29, "column": 24}}, "15": {"start": {"line": 31, "column": 4}, "end": {"line": 31, "column": 65}}, "16": {"start": {"line": 32, "column": 4}, "end": {"line": 32, "column": 76}}, "17": {"start": {"line": 37, "column": 0}, "end": {"line": 49, "column": 3}}, "18": {"start": {"line": 38, "column": 2}, "end": {"line": 48, "column": 3}}, "19": {"start": {"line": 39, "column": 4}, "end": {"line": 39, "column": 50}}, "20": {"start": {"line": 42, "column": 23}, "end": {"line": 42, "column": 69}}, "21": {"start": {"line": 44, "column": 4}, "end": {"line": 44, "column": 25}}, "22": {"start": {"line": 46, "column": 4}, "end": {"line": 46, "column": 62}}, "23": {"start": {"line": 47, "column": 4}, "end": {"line": 47, "column": 73}}, "24": {"start": {"line": 52, "column": 0}, "end": {"line": 64, "column": 3}}, "25": {"start": {"line": 53, "column": 2}, "end": {"line": 63, "column": 3}}, "26": {"start": {"line": 54, "column": 4}, "end": {"line": 54, "column": 44}}, "27": {"start": {"line": 57, "column": 21}, "end": {"line": 57, "column": 61}}, "28": {"start": {"line": 59, "column": 4}, "end": {"line": 59, "column": 23}}, "29": {"start": {"line": 61, "column": 4}, "end": {"line": 61, "column": 56}}, "30": {"start": {"line": 62, "column": 4}, "end": {"line": 62, "column": 67}}, "31": {"start": {"line": 67, "column": 0}, "end": {"line": 79, "column": 3}}, "32": {"start": {"line": 68, "column": 2}, "end": {"line": 78, "column": 3}}, "33": {"start": {"line": 69, "column": 4}, "end": {"line": 69, "column": 52}}, "34": {"start": {"line": 72, "column": 24}, "end": {"line": 72, "column": 73}}, "35": {"start": {"line": 74, "column": 4}, "end": {"line": 74, "column": 26}}, "36": {"start": {"line": 76, "column": 4}, "end": {"line": 76, "column": 65}}, "37": {"start": {"line": 77, "column": 4}, "end": {"line": 77, "column": 76}}, "38": {"start": {"line": 82, "column": 0}, "end": {"line": 86, "column": 3}}, "39": {"start": {"line": 83, "column": 2}, "end": {"line": 83, "column": 48}}, "40": {"start": {"line": 85, "column": 2}, "end": {"line": 85, "column": 98}}, "41": {"start": {"line": 89, "column": 0}, "end": {"line": 93, "column": 3}}, "42": {"start": {"line": 90, "column": 2}, "end": {"line": 90, "column": 48}}, "43": {"start": {"line": 92, "column": 2}, "end": {"line": 92, "column": 99}}, "44": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 22}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 7, "column": 23}, "end": {"line": 7, "column": 28}}, "loc": {"start": {"line": 7, "column": 77}, "end": {"line": 19, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 22, "column": 36}, "end": {"line": 22, "column": 41}}, "loc": {"start": {"line": 22, "column": 90}, "end": {"line": 34, "column": 1}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 37, "column": 33}, "end": {"line": 37, "column": 38}}, "loc": {"start": {"line": 37, "column": 87}, "end": {"line": 49, "column": 1}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 52, "column": 27}, "end": {"line": 52, "column": 32}}, "loc": {"start": {"line": 52, "column": 81}, "end": {"line": 64, "column": 1}}}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 67, "column": 36}, "end": {"line": 67, "column": 41}}, "loc": {"start": {"line": 67, "column": 90}, "end": {"line": 79, "column": 1}}}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 82, "column": 33}, "end": {"line": 82, "column": 34}}, "loc": {"start": {"line": 82, "column": 72}, "end": {"line": 86, "column": 1}}}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 89, "column": 34}, "end": {"line": 89, "column": 35}}, "loc": {"start": {"line": 89, "column": 73}, "end": {"line": 93, "column": 1}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "b": {}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/documents.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/documents.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 52}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 49}}, "2": {"start": {"line": 4, "column": 15}, "end": {"line": 4, "column": 23}}, "3": {"start": {"line": 7, "column": 0}, "end": {"line": 19, "column": 3}}, "4": {"start": {"line": 8, "column": 2}, "end": {"line": 18, "column": 3}}, "5": {"start": {"line": 9, "column": 4}, "end": {"line": 9, "column": 49}}, "6": {"start": {"line": 12, "column": 28}, "end": {"line": 12, "column": 73}}, "7": {"start": {"line": 14, "column": 4}, "end": {"line": 14, "column": 30}}, "8": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 61}}, "9": {"start": {"line": 17, "column": 4}, "end": {"line": 17, "column": 72}}, "10": {"start": {"line": 22, "column": 0}, "end": {"line": 34, "column": 3}}, "11": {"start": {"line": 23, "column": 2}, "end": {"line": 33, "column": 3}}, "12": {"start": {"line": 24, "column": 4}, "end": {"line": 24, "column": 47}}, "13": {"start": {"line": 27, "column": 18}, "end": {"line": 27, "column": 61}}, "14": {"start": {"line": 29, "column": 4}, "end": {"line": 29, "column": 20}}, "15": {"start": {"line": 31, "column": 4}, "end": {"line": 31, "column": 59}}, "16": {"start": {"line": 32, "column": 4}, "end": {"line": 32, "column": 70}}, "17": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 22}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 7, "column": 22}, "end": {"line": 7, "column": 27}}, "loc": {"start": {"line": 7, "column": 76}, "end": {"line": 19, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 22, "column": 21}, "end": {"line": 22, "column": 26}}, "loc": {"start": {"line": 22, "column": 75}, "end": {"line": 34, "column": 1}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/index.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/index.ts", "statementMap": {"0": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 16}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 44}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 30}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 38}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 42}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 42}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 44}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 42}}, "8": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": 47}}, "9": {"start": {"line": 13, "column": 2}, "end": {"line": 13, "column": 33}}, "10": {"start": {"line": 14, "column": 2}, "end": {"line": 14, "column": 41}}, "11": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": 45}}, "12": {"start": {"line": 16, "column": 2}, "end": {"line": 16, "column": 45}}, "13": {"start": {"line": 17, "column": 2}, "end": {"line": 17, "column": 47}}, "14": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 44}}}, "fnMap": {"0": {"name": "setupRoutes", "decl": {"start": {"line": 10, "column": 16}, "end": {"line": 10, "column": 27}}, "loc": {"start": {"line": 10, "column": 40}, "end": {"line": 19, "column": 1}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0}, "f": {"0": 0}, "b": {}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/kyc.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/kyc.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 52}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 42}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 20}}, "3": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 48}}, "4": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 49}}, "5": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 44}}, "6": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 46}}, "7": {"start": {"line": 10, "column": 15}, "end": {"line": 10, "column": 23}}, "8": {"start": {"line": 13, "column": 0}, "end": {"line": 36, "column": 3}}, "9": {"start": {"line": 14, "column": 2}, "end": {"line": 35, "column": 3}}, "10": {"start": {"line": 15, "column": 32}, "end": {"line": 15, "column": 40}}, "11": {"start": {"line": 18, "column": 19}, "end": {"line": 21, "column": 33}}, "12": {"start": {"line": 24, "column": 18}, "end": {"line": 24, "column": 83}}, "13": {"start": {"line": 26, "column": 4}, "end": {"line": 31, "column": 7}}, "14": {"start": {"line": 33, "column": 4}, "end": {"line": 33, "column": 75}}, "15": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 61}}, "16": {"start": {"line": 39, "column": 0}, "end": {"line": 67, "column": 3}}, "17": {"start": {"line": 40, "column": 2}, "end": {"line": 66, "column": 3}}, "18": {"start": {"line": 41, "column": 18}, "end": {"line": 41, "column": 36}}, "19": {"start": {"line": 42, "column": 4}, "end": {"line": 45, "column": 5}}, "20": {"start": {"line": 43, "column": 6}, "end": {"line": 43, "column": 63}}, "21": {"start": {"line": 44, "column": 6}, "end": {"line": 44, "column": 13}}, "22": {"start": {"line": 47, "column": 27}, "end": {"line": 47, "column": 35}}, "23": {"start": {"line": 50, "column": 4}, "end": {"line": 56, "column": 5}}, "24": {"start": {"line": 52, "column": 6}, "end": {"line": 52, "column": 100}}, "25": {"start": {"line": 55, "column": 6}, "end": {"line": 55, "column": 31}}, "26": {"start": {"line": 58, "column": 4}, "end": {"line": 62, "column": 7}}, "27": {"start": {"line": 64, "column": 4}, "end": {"line": 64, "column": 84}}, "28": {"start": {"line": 65, "column": 4}, "end": {"line": 65, "column": 61}}, "29": {"start": {"line": 70, "column": 0}, "end": {"line": 82, "column": 3}}, "30": {"start": {"line": 71, "column": 2}, "end": {"line": 81, "column": 3}}, "31": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 42}}, "32": {"start": {"line": 75, "column": 21}, "end": {"line": 75, "column": 59}}, "33": {"start": {"line": 77, "column": 4}, "end": {"line": 77, "column": 23}}, "34": {"start": {"line": 79, "column": 4}, "end": {"line": 79, "column": 54}}, "35": {"start": {"line": 80, "column": 4}, "end": {"line": 80, "column": 65}}, "36": {"start": {"line": 85, "column": 0}, "end": {"line": 89, "column": 3}}, "37": {"start": {"line": 86, "column": 2}, "end": {"line": 86, "column": 40}}, "38": {"start": {"line": 88, "column": 2}, "end": {"line": 88, "column": 90}}, "39": {"start": {"line": 91, "column": 0}, "end": {"line": 91, "column": 22}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 13, "column": 25}, "end": {"line": 13, "column": 30}}, "loc": {"start": {"line": 13, "column": 78}, "end": {"line": 36, "column": 1}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 39, "column": 53}, "end": {"line": 39, "column": 58}}, "loc": {"start": {"line": 39, "column": 102}, "end": {"line": 67, "column": 1}}}, "2": {"name": "(anonymous_3)", "decl": {"start": {"line": 70, "column": 21}, "end": {"line": 70, "column": 26}}, "loc": {"start": {"line": 70, "column": 75}, "end": {"line": 82, "column": 1}}}, "3": {"name": "(anonymous_4)", "decl": {"start": {"line": 85, "column": 21}, "end": {"line": 85, "column": 22}}, "loc": {"start": {"line": 85, "column": 60}, "end": {"line": 89, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 42, "column": 4}, "end": {"line": 45, "column": 5}}, "type": "if", "locations": [{"start": {"line": 42, "column": 4}, "end": {"line": 45, "column": 5}}]}, "1": {"loc": {"start": {"line": 42, "column": 8}, "end": {"line": 42, "column": 36}}, "type": "binary-expr", "locations": [{"start": {"line": 42, "column": 8}, "end": {"line": 42, "column": 14}}, {"start": {"line": 42, "column": 18}, "end": {"line": 42, "column": 36}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0], "1": [0, 0]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/regulatory.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/regulatory.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 52}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 42}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 49}}, "3": {"start": {"line": 5, "column": 15}, "end": {"line": 5, "column": 23}}, "4": {"start": {"line": 8, "column": 0}, "end": {"line": 31, "column": 3}}, "5": {"start": {"line": 9, "column": 2}, "end": {"line": 30, "column": 3}}, "6": {"start": {"line": 10, "column": 58}, "end": {"line": 10, "column": 66}}, "7": {"start": {"line": 12, "column": 4}, "end": {"line": 15, "column": 5}}, "8": {"start": {"line": 13, "column": 6}, "end": {"line": 13, "column": 86}}, "9": {"start": {"line": 14, "column": 6}, "end": {"line": 14, "column": 13}}, "10": {"start": {"line": 18, "column": 23}, "end": {"line": 18, "column": 45}}, "11": {"start": {"line": 20, "column": 4}, "end": {"line": 20, "column": 100}}, "12": {"start": {"line": 22, "column": 4}, "end": {"line": 26, "column": 7}}, "13": {"start": {"line": 28, "column": 4}, "end": {"line": 28, "column": 85}}, "14": {"start": {"line": 29, "column": 4}, "end": {"line": 29, "column": 61}}, "15": {"start": {"line": 34, "column": 0}, "end": {"line": 60, "column": 3}}, "16": {"start": {"line": 35, "column": 2}, "end": {"line": 59, "column": 3}}, "17": {"start": {"line": 36, "column": 4}, "end": {"line": 36, "column": 51}}, "18": {"start": {"line": 39, "column": 20}, "end": {"line": 39, "column": 67}}, "19": {"start": {"line": 42, "column": 30}, "end": {"line": 53, "column": 7}}, "20": {"start": {"line": 42, "column": 62}, "end": {"line": 53, "column": 6}}, "21": {"start": {"line": 55, "column": 4}, "end": {"line": 55, "column": 32}}, "22": {"start": {"line": 57, "column": 4}, "end": {"line": 57, "column": 63}}, "23": {"start": {"line": 58, "column": 4}, "end": {"line": 58, "column": 74}}, "24": {"start": {"line": 63, "column": 0}, "end": {"line": 67, "column": 3}}, "25": {"start": {"line": 64, "column": 2}, "end": {"line": 64, "column": 47}}, "26": {"start": {"line": 66, "column": 2}, "end": {"line": 66, "column": 97}}, "27": {"start": {"line": 70, "column": 0}, "end": {"line": 74, "column": 3}}, "28": {"start": {"line": 71, "column": 2}, "end": {"line": 71, "column": 49}}, "29": {"start": {"line": 73, "column": 2}, "end": {"line": 73, "column": 99}}, "30": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 22}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 8, "column": 31}, "end": {"line": 8, "column": 36}}, "loc": {"start": {"line": 8, "column": 84}, "end": {"line": 31, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 34, "column": 23}, "end": {"line": 34, "column": 28}}, "loc": {"start": {"line": 34, "column": 77}, "end": {"line": 60, "column": 1}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 42, "column": 42}, "end": {"line": 42, "column": 43}}, "loc": {"start": {"line": 42, "column": 62}, "end": {"line": 53, "column": 6}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 63, "column": 21}, "end": {"line": 63, "column": 22}}, "loc": {"start": {"line": 63, "column": 60}, "end": {"line": 67, "column": 1}}}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 70, "column": 23}, "end": {"line": 70, "column": 24}}, "loc": {"start": {"line": 70, "column": 62}, "end": {"line": 74, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 12, "column": 4}, "end": {"line": 15, "column": 5}}, "type": "if", "locations": [{"start": {"line": 12, "column": 4}, "end": {"line": 15, "column": 5}}]}, "1": {"loc": {"start": {"line": 12, "column": 8}, "end": {"line": 12, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 12, "column": 8}, "end": {"line": 12, "column": 26}}, {"start": {"line": 12, "column": 30}, "end": {"line": 12, "column": 60}}]}, "2": {"loc": {"start": {"line": 51, "column": 14}, "end": {"line": 51, "column": 70}}, "type": "cond-expr", "locations": [{"start": {"line": 51, "column": 38}, "end": {"line": 51, "column": 55}}, {"start": {"line": 51, "column": 58}, "end": {"line": 51, "column": 70}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/reports.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/reports.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 52}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 42}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 48}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 49}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 50}}, "5": {"start": {"line": 7, "column": 15}, "end": {"line": 7, "column": 23}}, "6": {"start": {"line": 10, "column": 0}, "end": {"line": 34, "column": 3}}, "7": {"start": {"line": 11, "column": 2}, "end": {"line": 33, "column": 3}}, "8": {"start": {"line": 12, "column": 27}, "end": {"line": 12, "column": 35}}, "9": {"start": {"line": 14, "column": 4}, "end": {"line": 17, "column": 5}}, "10": {"start": {"line": 15, "column": 6}, "end": {"line": 15, "column": 61}}, "11": {"start": {"line": 16, "column": 6}, "end": {"line": 16, "column": 13}}, "12": {"start": {"line": 20, "column": 19}, "end": {"line": 23, "column": 42}}, "13": {"start": {"line": 25, "column": 4}, "end": {"line": 29, "column": 7}}, "14": {"start": {"line": 31, "column": 4}, "end": {"line": 31, "column": 78}}, "15": {"start": {"line": 32, "column": 4}, "end": {"line": 32, "column": 61}}, "16": {"start": {"line": 37, "column": 0}, "end": {"line": 49, "column": 3}}, "17": {"start": {"line": 38, "column": 2}, "end": {"line": 48, "column": 3}}, "18": {"start": {"line": 39, "column": 4}, "end": {"line": 39, "column": 47}}, "19": {"start": {"line": 42, "column": 26}, "end": {"line": 42, "column": 69}}, "20": {"start": {"line": 44, "column": 4}, "end": {"line": 44, "column": 28}}, "21": {"start": {"line": 46, "column": 4}, "end": {"line": 46, "column": 59}}, "22": {"start": {"line": 47, "column": 4}, "end": {"line": 47, "column": 70}}, "23": {"start": {"line": 52, "column": 0}, "end": {"line": 56, "column": 3}}, "24": {"start": {"line": 53, "column": 2}, "end": {"line": 53, "column": 43}}, "25": {"start": {"line": 55, "column": 2}, "end": {"line": 55, "column": 93}}, "26": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 22}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 10, "column": 25}, "end": {"line": 10, "column": 30}}, "loc": {"start": {"line": 10, "column": 78}, "end": {"line": 34, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 37, "column": 22}, "end": {"line": 37, "column": 27}}, "loc": {"start": {"line": 37, "column": 76}, "end": {"line": 49, "column": 1}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 52, "column": 21}, "end": {"line": 52, "column": 22}}, "loc": {"start": {"line": 52, "column": 60}, "end": {"line": 56, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 14, "column": 4}, "end": {"line": 17, "column": 5}}, "type": "if", "locations": [{"start": {"line": 14, "column": 4}, "end": {"line": 17, "column": 5}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/workflows.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/workflows.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 52}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 42}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 49}}, "3": {"start": {"line": 5, "column": 15}, "end": {"line": 5, "column": 23}}, "4": {"start": {"line": 8, "column": 0}, "end": {"line": 68, "column": 3}}, "5": {"start": {"line": 9, "column": 2}, "end": {"line": 67, "column": 3}}, "6": {"start": {"line": 10, "column": 27}, "end": {"line": 10, "column": 37}}, "7": {"start": {"line": 12, "column": 4}, "end": {"line": 15, "column": 5}}, "8": {"start": {"line": 13, "column": 6}, "end": {"line": 13, "column": 65}}, "9": {"start": {"line": 14, "column": 6}, "end": {"line": 14, "column": 13}}, "10": {"start": {"line": 18, "column": 19}, "end": {"line": 18, "column": 58}}, "11": {"start": {"line": 19, "column": 19}, "end": {"line": 19, "column": 43}}, "12": {"start": {"line": 22, "column": 4}, "end": {"line": 25, "column": 5}}, "13": {"start": {"line": 23, "column": 6}, "end": {"line": 23, "column": 71}}, "14": {"start": {"line": 24, "column": 6}, "end": {"line": 24, "column": 13}}, "15": {"start": {"line": 28, "column": 44}, "end": {"line": 28, "column": 46}}, "16": {"start": {"line": 29, "column": 4}, "end": {"line": 38, "column": 5}}, "17": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 92}}, "18": {"start": {"line": 31, "column": 6}, "end": {"line": 31, "column": 90}}, "19": {"start": {"line": 32, "column": 6}, "end": {"line": 32, "column": 78}}, "20": {"start": {"line": 33, "column": 6}, "end": {"line": 33, "column": 84}}, "21": {"start": {"line": 34, "column": 6}, "end": {"line": 34, "column": 82}}, "22": {"start": {"line": 37, "column": 6}, "end": {"line": 37, "column": 69}}, "23": {"start": {"line": 41, "column": 23}, "end": {"line": 41, "column": 36}}, "24": {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 85}}, "25": {"start": {"line": 44, "column": 4}, "end": {"line": 56, "column": 5}}, "26": {"start": {"line": 45, "column": 6}, "end": {"line": 45, "column": 33}}, "27": {"start": {"line": 46, "column": 6}, "end": {"line": 46, "column": 97}}, "28": {"start": {"line": 47, "column": 11}, "end": {"line": 56, "column": 5}}, "29": {"start": {"line": 48, "column": 6}, "end": {"line": 48, "column": 30}}, "30": {"start": {"line": 49, "column": 6}, "end": {"line": 49, "column": 78}}, "31": {"start": {"line": 50, "column": 11}, "end": {"line": 56, "column": 5}}, "32": {"start": {"line": 51, "column": 6}, "end": {"line": 51, "column": 31}}, "33": {"start": {"line": 52, "column": 11}, "end": {"line": 56, "column": 5}}, "34": {"start": {"line": 53, "column": 6}, "end": {"line": 53, "column": 31}}, "35": {"start": {"line": 55, "column": 6}, "end": {"line": 55, "column": 113}}, "36": {"start": {"line": 58, "column": 4}, "end": {"line": 63, "column": 7}}, "37": {"start": {"line": 65, "column": 4}, "end": {"line": 65, "column": 84}}, "38": {"start": {"line": 66, "column": 4}, "end": {"line": 66, "column": 58}}, "39": {"start": {"line": 71, "column": 0}, "end": {"line": 92, "column": 3}}, "40": {"start": {"line": 72, "column": 2}, "end": {"line": 91, "column": 3}}, "41": {"start": {"line": 73, "column": 27}, "end": {"line": 73, "column": 37}}, "42": {"start": {"line": 75, "column": 4}, "end": {"line": 78, "column": 5}}, "43": {"start": {"line": 76, "column": 6}, "end": {"line": 76, "column": 65}}, "44": {"start": {"line": 77, "column": 6}, "end": {"line": 77, "column": 13}}, "45": {"start": {"line": 81, "column": 19}, "end": {"line": 81, "column": 58}}, "46": {"start": {"line": 82, "column": 19}, "end": {"line": 82, "column": 43}}, "47": {"start": {"line": 84, "column": 4}, "end": {"line": 87, "column": 7}}, "48": {"start": {"line": 89, "column": 4}, "end": {"line": 89, "column": 84}}, "49": {"start": {"line": 90, "column": 4}, "end": {"line": 90, "column": 75}}, "50": {"start": {"line": 95, "column": 0}, "end": {"line": 107, "column": 3}}, "51": {"start": {"line": 96, "column": 2}, "end": {"line": 106, "column": 3}}, "52": {"start": {"line": 97, "column": 4}, "end": {"line": 97, "column": 48}}, "53": {"start": {"line": 100, "column": 28}, "end": {"line": 100, "column": 73}}, "54": {"start": {"line": 102, "column": 4}, "end": {"line": 102, "column": 30}}, "55": {"start": {"line": 104, "column": 4}, "end": {"line": 104, "column": 61}}, "56": {"start": {"line": 105, "column": 4}, "end": {"line": 105, "column": 72}}, "57": {"start": {"line": 110, "column": 0}, "end": {"line": 114, "column": 3}}, "58": {"start": {"line": 111, "column": 2}, "end": {"line": 111, "column": 45}}, "59": {"start": {"line": 113, "column": 2}, "end": {"line": 113, "column": 95}}, "60": {"start": {"line": 117, "column": 0}, "end": {"line": 121, "column": 3}}, "61": {"start": {"line": 118, "column": 2}, "end": {"line": 118, "column": 47}}, "62": {"start": {"line": 120, "column": 2}, "end": {"line": 120, "column": 97}}, "63": {"start": {"line": 123, "column": 0}, "end": {"line": 123, "column": 22}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 8, "column": 34}, "end": {"line": 8, "column": 39}}, "loc": {"start": {"line": 8, "column": 87}, "end": {"line": 68, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 71, "column": 34}, "end": {"line": 71, "column": 39}}, "loc": {"start": {"line": 71, "column": 87}, "end": {"line": 92, "column": 1}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 95, "column": 22}, "end": {"line": 95, "column": 27}}, "loc": {"start": {"line": 95, "column": 76}, "end": {"line": 107, "column": 1}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 110, "column": 21}, "end": {"line": 110, "column": 22}}, "loc": {"start": {"line": 110, "column": 60}, "end": {"line": 114, "column": 1}}}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 117, "column": 23}, "end": {"line": 117, "column": 24}}, "loc": {"start": {"line": 117, "column": 62}, "end": {"line": 121, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 12, "column": 4}, "end": {"line": 15, "column": 5}}, "type": "if", "locations": [{"start": {"line": 12, "column": 4}, "end": {"line": 15, "column": 5}}]}, "1": {"loc": {"start": {"line": 22, "column": 4}, "end": {"line": 25, "column": 5}}, "type": "if", "locations": [{"start": {"line": 22, "column": 4}, "end": {"line": 25, "column": 5}}]}, "2": {"loc": {"start": {"line": 44, "column": 4}, "end": {"line": 56, "column": 5}}, "type": "if", "locations": [{"start": {"line": 44, "column": 4}, "end": {"line": 56, "column": 5}}, {"start": {"line": 47, "column": 11}, "end": {"line": 56, "column": 5}}]}, "3": {"loc": {"start": {"line": 44, "column": 8}, "end": {"line": 44, "column": 68}}, "type": "binary-expr", "locations": [{"start": {"line": 44, "column": 8}, "end": {"line": 44, "column": 37}}, {"start": {"line": 44, "column": 41}, "end": {"line": 44, "column": 68}}]}, "4": {"loc": {"start": {"line": 47, "column": 11}, "end": {"line": 56, "column": 5}}, "type": "if", "locations": [{"start": {"line": 47, "column": 11}, "end": {"line": 56, "column": 5}}, {"start": {"line": 50, "column": 11}, "end": {"line": 56, "column": 5}}]}, "5": {"loc": {"start": {"line": 47, "column": 15}, "end": {"line": 47, "column": 70}}, "type": "binary-expr", "locations": [{"start": {"line": 47, "column": 15}, "end": {"line": 47, "column": 40}}, {"start": {"line": 47, "column": 44}, "end": {"line": 47, "column": 70}}]}, "6": {"loc": {"start": {"line": 50, "column": 11}, "end": {"line": 56, "column": 5}}, "type": "if", "locations": [{"start": {"line": 50, "column": 11}, "end": {"line": 56, "column": 5}}, {"start": {"line": 52, "column": 11}, "end": {"line": 56, "column": 5}}]}, "7": {"loc": {"start": {"line": 50, "column": 15}, "end": {"line": 50, "column": 74}}, "type": "binary-expr", "locations": [{"start": {"line": 50, "column": 15}, "end": {"line": 50, "column": 42}}, {"start": {"line": 50, "column": 46}, "end": {"line": 50, "column": 74}}]}, "8": {"loc": {"start": {"line": 52, "column": 11}, "end": {"line": 56, "column": 5}}, "type": "if", "locations": [{"start": {"line": 52, "column": 11}, "end": {"line": 56, "column": 5}}, {"start": {"line": 54, "column": 11}, "end": {"line": 56, "column": 5}}]}, "9": {"loc": {"start": {"line": 75, "column": 4}, "end": {"line": 78, "column": 5}}, "type": "if", "locations": [{"start": {"line": 75, "column": 4}, "end": {"line": 78, "column": 5}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {"0": [0], "1": [0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/services/complianceApi.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/services/complianceApi.ts", "statementMap": {"0": {"start": {"line": 3, "column": 21}, "end": {"line": 3, "column": 44}}, "1": {"start": {"line": 115, "column": 13}, "end": {"line": 455, "column": 2}}, "2": {"start": {"line": 122, "column": 41}, "end": {"line": 128, "column": 6}}, "3": {"start": {"line": 130, "column": 21}, "end": {"line": 136, "column": 6}}, "4": {"start": {"line": 138, "column": 4}, "end": {"line": 140, "column": 5}}, "5": {"start": {"line": 139, "column": 6}, "end": {"line": 139, "column": 72}}, "6": {"start": {"line": 142, "column": 4}, "end": {"line": 142, "column": 27}}, "7": {"start": {"line": 147, "column": 21}, "end": {"line": 147, "column": 35}}, "8": {"start": {"line": 148, "column": 4}, "end": {"line": 148, "column": 34}}, "9": {"start": {"line": 149, "column": 4}, "end": {"line": 149, "column": 50}}, "10": {"start": {"line": 150, "column": 4}, "end": {"line": 150, "column": 55}}, "11": {"start": {"line": 152, "column": 21}, "end": {"line": 155, "column": 6}}, "12": {"start": {"line": 157, "column": 4}, "end": {"line": 159, "column": 5}}, "13": {"start": {"line": 158, "column": 6}, "end": {"line": 158, "column": 72}}, "14": {"start": {"line": 161, "column": 4}, "end": {"line": 161, "column": 27}}, "15": {"start": {"line": 169, "column": 32}, "end": {"line": 175, "column": 6}}, "16": {"start": {"line": 177, "column": 21}, "end": {"line": 183, "column": 6}}, "17": {"start": {"line": 185, "column": 4}, "end": {"line": 187, "column": 5}}, "18": {"start": {"line": 186, "column": 6}, "end": {"line": 186, "column": 70}}, "19": {"start": {"line": 189, "column": 4}, "end": {"line": 189, "column": 27}}, "20": {"start": {"line": 194, "column": 21}, "end": {"line": 194, "column": 35}}, "21": {"start": {"line": 195, "column": 4}, "end": {"line": 195, "column": 46}}, "22": {"start": {"line": 197, "column": 4}, "end": {"line": 199, "column": 7}}, "23": {"start": {"line": 198, "column": 6}, "end": {"line": 198, "column": 49}}, "24": {"start": {"line": 201, "column": 21}, "end": {"line": 204, "column": 6}}, "25": {"start": {"line": 206, "column": 4}, "end": {"line": 208, "column": 5}}, "26": {"start": {"line": 207, "column": 6}, "end": {"line": 207, "column": 56}}, "27": {"start": {"line": 210, "column": 4}, "end": {"line": 210, "column": 27}}, "28": {"start": {"line": 222, "column": 21}, "end": {"line": 228, "column": 6}}, "29": {"start": {"line": 230, "column": 4}, "end": {"line": 232, "column": 5}}, "30": {"start": {"line": 231, "column": 6}, "end": {"line": 231, "column": 62}}, "31": {"start": {"line": 234, "column": 4}, "end": {"line": 234, "column": 27}}, "32": {"start": {"line": 243, "column": 21}, "end": {"line": 249, "column": 6}}, "33": {"start": {"line": 251, "column": 4}, "end": {"line": 253, "column": 5}}, "34": {"start": {"line": 252, "column": 6}, "end": {"line": 252, "column": 68}}, "35": {"start": {"line": 255, "column": 4}, "end": {"line": 255, "column": 27}}, "36": {"start": {"line": 260, "column": 21}, "end": {"line": 260, "column": 85}}, "37": {"start": {"line": 262, "column": 4}, "end": {"line": 264, "column": 5}}, "38": {"start": {"line": 263, "column": 6}, "end": {"line": 263, "column": 55}}, "39": {"start": {"line": 266, "column": 4}, "end": {"line": 266, "column": 27}}, "40": {"start": {"line": 271, "column": 21}, "end": {"line": 271, "column": 85}}, "41": {"start": {"line": 273, "column": 4}, "end": {"line": 275, "column": 5}}, "42": {"start": {"line": 274, "column": 6}, "end": {"line": 274, "column": 55}}, "43": {"start": {"line": 277, "column": 4}, "end": {"line": 277, "column": 27}}, "44": {"start": {"line": 282, "column": 21}, "end": {"line": 282, "column": 73}}, "45": {"start": {"line": 284, "column": 4}, "end": {"line": 286, "column": 5}}, "46": {"start": {"line": 285, "column": 6}, "end": {"line": 285, "column": 59}}, "47": {"start": {"line": 288, "column": 4}, "end": {"line": 288, "column": 27}}, "48": {"start": {"line": 292, "column": 21}, "end": {"line": 292, "column": 86}}, "49": {"start": {"line": 294, "column": 4}, "end": {"line": 296, "column": 5}}, "50": {"start": {"line": 295, "column": 6}, "end": {"line": 295, "column": 62}}, "51": {"start": {"line": 298, "column": 4}, "end": {"line": 298, "column": 27}}, "52": {"start": {"line": 302, "column": 21}, "end": {"line": 302, "column": 83}}, "53": {"start": {"line": 304, "column": 4}, "end": {"line": 306, "column": 5}}, "54": {"start": {"line": 305, "column": 6}, "end": {"line": 305, "column": 59}}, "55": {"start": {"line": 308, "column": 4}, "end": {"line": 308, "column": 27}}, "56": {"start": {"line": 312, "column": 21}, "end": {"line": 312, "column": 77}}, "57": {"start": {"line": 314, "column": 4}, "end": {"line": 316, "column": 5}}, "58": {"start": {"line": 315, "column": 6}, "end": {"line": 315, "column": 53}}, "59": {"start": {"line": 318, "column": 4}, "end": {"line": 318, "column": 27}}, "60": {"start": {"line": 322, "column": 21}, "end": {"line": 322, "column": 86}}, "61": {"start": {"line": 324, "column": 4}, "end": {"line": 326, "column": 5}}, "62": {"start": {"line": 325, "column": 6}, "end": {"line": 325, "column": 62}}, "63": {"start": {"line": 328, "column": 4}, "end": {"line": 328, "column": 27}}, "64": {"start": {"line": 333, "column": 21}, "end": {"line": 333, "column": 72}}, "65": {"start": {"line": 335, "column": 4}, "end": {"line": 337, "column": 5}}, "66": {"start": {"line": 336, "column": 6}, "end": {"line": 336, "column": 58}}, "67": {"start": {"line": 339, "column": 4}, "end": {"line": 339, "column": 27}}, "68": {"start": {"line": 343, "column": 21}, "end": {"line": 343, "column": 71}}, "69": {"start": {"line": 345, "column": 4}, "end": {"line": 347, "column": 5}}, "70": {"start": {"line": 346, "column": 6}, "end": {"line": 346, "column": 56}}, "71": {"start": {"line": 349, "column": 4}, "end": {"line": 349, "column": 27}}, "72": {"start": {"line": 354, "column": 21}, "end": {"line": 354, "column": 74}}, "73": {"start": {"line": 356, "column": 4}, "end": {"line": 358, "column": 5}}, "74": {"start": {"line": 357, "column": 6}, "end": {"line": 357, "column": 60}}, "75": {"start": {"line": 360, "column": 4}, "end": {"line": 360, "column": 27}}, "76": {"start": {"line": 364, "column": 21}, "end": {"line": 364, "column": 72}}, "77": {"start": {"line": 366, "column": 4}, "end": {"line": 368, "column": 5}}, "78": {"start": {"line": 367, "column": 6}, "end": {"line": 367, "column": 58}}, "79": {"start": {"line": 370, "column": 4}, "end": {"line": 370, "column": 27}}, "80": {"start": {"line": 374, "column": 21}, "end": {"line": 374, "column": 74}}, "81": {"start": {"line": 376, "column": 4}, "end": {"line": 378, "column": 5}}, "82": {"start": {"line": 377, "column": 6}, "end": {"line": 377, "column": 59}}, "83": {"start": {"line": 380, "column": 4}, "end": {"line": 380, "column": 27}}, "84": {"start": {"line": 385, "column": 21}, "end": {"line": 385, "column": 65}}, "85": {"start": {"line": 387, "column": 4}, "end": {"line": 389, "column": 5}}, "86": {"start": {"line": 388, "column": 6}, "end": {"line": 388, "column": 51}}, "87": {"start": {"line": 391, "column": 4}, "end": {"line": 391, "column": 27}}, "88": {"start": {"line": 395, "column": 21}, "end": {"line": 395, "column": 65}}, "89": {"start": {"line": 397, "column": 4}, "end": {"line": 399, "column": 5}}, "90": {"start": {"line": 398, "column": 6}, "end": {"line": 398, "column": 51}}, "91": {"start": {"line": 401, "column": 4}, "end": {"line": 401, "column": 27}}, "92": {"start": {"line": 406, "column": 21}, "end": {"line": 406, "column": 70}}, "93": {"start": {"line": 408, "column": 4}, "end": {"line": 410, "column": 5}}, "94": {"start": {"line": 409, "column": 6}, "end": {"line": 409, "column": 56}}, "95": {"start": {"line": 412, "column": 4}, "end": {"line": 412, "column": 27}}, "96": {"start": {"line": 416, "column": 21}, "end": {"line": 416, "column": 69}}, "97": {"start": {"line": 418, "column": 4}, "end": {"line": 420, "column": 5}}, "98": {"start": {"line": 419, "column": 6}, "end": {"line": 419, "column": 54}}, "99": {"start": {"line": 422, "column": 4}, "end": {"line": 422, "column": 27}}, "100": {"start": {"line": 427, "column": 21}, "end": {"line": 427, "column": 72}}, "101": {"start": {"line": 429, "column": 4}, "end": {"line": 431, "column": 5}}, "102": {"start": {"line": 430, "column": 6}, "end": {"line": 430, "column": 58}}, "103": {"start": {"line": 433, "column": 4}, "end": {"line": 433, "column": 27}}, "104": {"start": {"line": 437, "column": 21}, "end": {"line": 437, "column": 71}}, "105": {"start": {"line": 439, "column": 4}, "end": {"line": 441, "column": 5}}, "106": {"start": {"line": 440, "column": 6}, "end": {"line": 440, "column": 56}}, "107": {"start": {"line": 443, "column": 4}, "end": {"line": 443, "column": 27}}, "108": {"start": {"line": 447, "column": 21}, "end": {"line": 447, "column": 73}}, "109": {"start": {"line": 449, "column": 4}, "end": {"line": 451, "column": 5}}, "110": {"start": {"line": 450, "column": 6}, "end": {"line": 450, "column": 58}}, "111": {"start": {"line": 453, "column": 4}, "end": {"line": 453, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 117, "column": 2}, "end": {"line": 117, "column": 7}}, "loc": {"start": {"line": 121, "column": 3}, "end": {"line": 143, "column": 3}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 146, "column": 2}, "end": {"line": 146, "column": 7}}, "loc": {"start": {"line": 146, "column": 87}, "end": {"line": 162, "column": 3}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 165, "column": 2}, "end": {"line": 165, "column": 7}}, "loc": {"start": {"line": 168, "column": 3}, "end": {"line": 190, "column": 3}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 193, "column": 2}, "end": {"line": 193, "column": 7}}, "loc": {"start": {"line": 193, "column": 64}, "end": {"line": 211, "column": 3}}}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 197, "column": 22}, "end": {"line": 197, "column": 23}}, "loc": {"start": {"line": 197, "column": 38}, "end": {"line": 199, "column": 5}}}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 214, "column": 2}, "end": {"line": 214, "column": 7}}, "loc": {"start": {"line": 221, "column": 3}, "end": {"line": 235, "column": 3}}}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 238, "column": 2}, "end": {"line": 238, "column": 7}}, "loc": {"start": {"line": 242, "column": 3}, "end": {"line": 256, "column": 3}}}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 259, "column": 2}, "end": {"line": 259, "column": 7}}, "loc": {"start": {"line": 259, "column": 44}, "end": {"line": 267, "column": 3}}}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 270, "column": 2}, "end": {"line": 270, "column": 7}}, "loc": {"start": {"line": 270, "column": 44}, "end": {"line": 278, "column": 3}}}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 281, "column": 2}, "end": {"line": 281, "column": 7}}, "loc": {"start": {"line": 281, "column": 27}, "end": {"line": 289, "column": 3}}}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 291, "column": 2}, "end": {"line": 291, "column": 7}}, "loc": {"start": {"line": 291, "column": 30}, "end": {"line": 299, "column": 3}}}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 301, "column": 2}, "end": {"line": 301, "column": 7}}, "loc": {"start": {"line": 301, "column": 27}, "end": {"line": 309, "column": 3}}}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 311, "column": 2}, "end": {"line": 311, "column": 7}}, "loc": {"start": {"line": 311, "column": 21}, "end": {"line": 319, "column": 3}}}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 321, "column": 2}, "end": {"line": 321, "column": 7}}, "loc": {"start": {"line": 321, "column": 30}, "end": {"line": 329, "column": 3}}}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 332, "column": 2}, "end": {"line": 332, "column": 7}}, "loc": {"start": {"line": 332, "column": 26}, "end": {"line": 340, "column": 3}}}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 342, "column": 2}, "end": {"line": 342, "column": 7}}, "loc": {"start": {"line": 342, "column": 24}, "end": {"line": 350, "column": 3}}}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 353, "column": 2}, "end": {"line": 353, "column": 7}}, "loc": {"start": {"line": 353, "column": 28}, "end": {"line": 361, "column": 3}}}, "17": {"name": "(anonymous_17)", "decl": {"start": {"line": 363, "column": 2}, "end": {"line": 363, "column": 7}}, "loc": {"start": {"line": 363, "column": 26}, "end": {"line": 371, "column": 3}}}, "18": {"name": "(anonymous_18)", "decl": {"start": {"line": 373, "column": 2}, "end": {"line": 373, "column": 7}}, "loc": {"start": {"line": 373, "column": 27}, "end": {"line": 381, "column": 3}}}, "19": {"name": "(anonymous_19)", "decl": {"start": {"line": 384, "column": 2}, "end": {"line": 384, "column": 7}}, "loc": {"start": {"line": 384, "column": 19}, "end": {"line": 392, "column": 3}}}, "20": {"name": "(anonymous_20)", "decl": {"start": {"line": 394, "column": 2}, "end": {"line": 394, "column": 7}}, "loc": {"start": {"line": 394, "column": 19}, "end": {"line": 402, "column": 3}}}, "21": {"name": "(anonymous_21)", "decl": {"start": {"line": 405, "column": 2}, "end": {"line": 405, "column": 7}}, "loc": {"start": {"line": 405, "column": 24}, "end": {"line": 413, "column": 3}}}, "22": {"name": "(anonymous_22)", "decl": {"start": {"line": 415, "column": 2}, "end": {"line": 415, "column": 7}}, "loc": {"start": {"line": 415, "column": 22}, "end": {"line": 423, "column": 3}}}, "23": {"name": "(anonymous_23)", "decl": {"start": {"line": 426, "column": 2}, "end": {"line": 426, "column": 7}}, "loc": {"start": {"line": 426, "column": 26}, "end": {"line": 434, "column": 3}}}, "24": {"name": "(anonymous_24)", "decl": {"start": {"line": 436, "column": 2}, "end": {"line": 436, "column": 7}}, "loc": {"start": {"line": 436, "column": 24}, "end": {"line": 444, "column": 3}}}, "25": {"name": "(anonymous_25)", "decl": {"start": {"line": 446, "column": 2}, "end": {"line": 446, "column": 7}}, "loc": {"start": {"line": 446, "column": 26}, "end": {"line": 454, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 138, "column": 4}, "end": {"line": 140, "column": 5}}, "type": "if", "locations": [{"start": {"line": 138, "column": 4}, "end": {"line": 140, "column": 5}}]}, "1": {"loc": {"start": {"line": 157, "column": 4}, "end": {"line": 159, "column": 5}}, "type": "if", "locations": [{"start": {"line": 157, "column": 4}, "end": {"line": 159, "column": 5}}]}, "2": {"loc": {"start": {"line": 185, "column": 4}, "end": {"line": 187, "column": 5}}, "type": "if", "locations": [{"start": {"line": 185, "column": 4}, "end": {"line": 187, "column": 5}}]}, "3": {"loc": {"start": {"line": 206, "column": 4}, "end": {"line": 208, "column": 5}}, "type": "if", "locations": [{"start": {"line": 206, "column": 4}, "end": {"line": 208, "column": 5}}]}, "4": {"loc": {"start": {"line": 230, "column": 4}, "end": {"line": 232, "column": 5}}, "type": "if", "locations": [{"start": {"line": 230, "column": 4}, "end": {"line": 232, "column": 5}}]}, "5": {"loc": {"start": {"line": 251, "column": 4}, "end": {"line": 253, "column": 5}}, "type": "if", "locations": [{"start": {"line": 251, "column": 4}, "end": {"line": 253, "column": 5}}]}, "6": {"loc": {"start": {"line": 262, "column": 4}, "end": {"line": 264, "column": 5}}, "type": "if", "locations": [{"start": {"line": 262, "column": 4}, "end": {"line": 264, "column": 5}}]}, "7": {"loc": {"start": {"line": 273, "column": 4}, "end": {"line": 275, "column": 5}}, "type": "if", "locations": [{"start": {"line": 273, "column": 4}, "end": {"line": 275, "column": 5}}]}, "8": {"loc": {"start": {"line": 284, "column": 4}, "end": {"line": 286, "column": 5}}, "type": "if", "locations": [{"start": {"line": 284, "column": 4}, "end": {"line": 286, "column": 5}}]}, "9": {"loc": {"start": {"line": 294, "column": 4}, "end": {"line": 296, "column": 5}}, "type": "if", "locations": [{"start": {"line": 294, "column": 4}, "end": {"line": 296, "column": 5}}]}, "10": {"loc": {"start": {"line": 304, "column": 4}, "end": {"line": 306, "column": 5}}, "type": "if", "locations": [{"start": {"line": 304, "column": 4}, "end": {"line": 306, "column": 5}}]}, "11": {"loc": {"start": {"line": 314, "column": 4}, "end": {"line": 316, "column": 5}}, "type": "if", "locations": [{"start": {"line": 314, "column": 4}, "end": {"line": 316, "column": 5}}]}, "12": {"loc": {"start": {"line": 324, "column": 4}, "end": {"line": 326, "column": 5}}, "type": "if", "locations": [{"start": {"line": 324, "column": 4}, "end": {"line": 326, "column": 5}}]}, "13": {"loc": {"start": {"line": 335, "column": 4}, "end": {"line": 337, "column": 5}}, "type": "if", "locations": [{"start": {"line": 335, "column": 4}, "end": {"line": 337, "column": 5}}]}, "14": {"loc": {"start": {"line": 345, "column": 4}, "end": {"line": 347, "column": 5}}, "type": "if", "locations": [{"start": {"line": 345, "column": 4}, "end": {"line": 347, "column": 5}}]}, "15": {"loc": {"start": {"line": 356, "column": 4}, "end": {"line": 358, "column": 5}}, "type": "if", "locations": [{"start": {"line": 356, "column": 4}, "end": {"line": 358, "column": 5}}]}, "16": {"loc": {"start": {"line": 366, "column": 4}, "end": {"line": 368, "column": 5}}, "type": "if", "locations": [{"start": {"line": 366, "column": 4}, "end": {"line": 368, "column": 5}}]}, "17": {"loc": {"start": {"line": 376, "column": 4}, "end": {"line": 378, "column": 5}}, "type": "if", "locations": [{"start": {"line": 376, "column": 4}, "end": {"line": 378, "column": 5}}]}, "18": {"loc": {"start": {"line": 387, "column": 4}, "end": {"line": 389, "column": 5}}, "type": "if", "locations": [{"start": {"line": 387, "column": 4}, "end": {"line": 389, "column": 5}}]}, "19": {"loc": {"start": {"line": 397, "column": 4}, "end": {"line": 399, "column": 5}}, "type": "if", "locations": [{"start": {"line": 397, "column": 4}, "end": {"line": 399, "column": 5}}]}, "20": {"loc": {"start": {"line": 408, "column": 4}, "end": {"line": 410, "column": 5}}, "type": "if", "locations": [{"start": {"line": 408, "column": 4}, "end": {"line": 410, "column": 5}}]}, "21": {"loc": {"start": {"line": 418, "column": 4}, "end": {"line": 420, "column": 5}}, "type": "if", "locations": [{"start": {"line": 418, "column": 4}, "end": {"line": 420, "column": 5}}]}, "22": {"loc": {"start": {"line": 429, "column": 4}, "end": {"line": 431, "column": 5}}, "type": "if", "locations": [{"start": {"line": 429, "column": 4}, "end": {"line": 431, "column": 5}}]}, "23": {"loc": {"start": {"line": 439, "column": 4}, "end": {"line": 441, "column": 5}}, "type": "if", "locations": [{"start": {"line": 439, "column": 4}, "end": {"line": 441, "column": 5}}]}, "24": {"loc": {"start": {"line": 449, "column": 4}, "end": {"line": 451, "column": 5}}, "type": "if", "locations": [{"start": {"line": 449, "column": 4}, "end": {"line": 451, "column": 5}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "110": 0, "111": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0], "4": [0], "5": [0], "6": [0], "7": [0], "8": [0], "9": [0], "10": [0], "11": [0], "12": [0], "13": [0], "14": [0], "15": [0], "16": [0], "17": [0], "18": [0], "19": [0], "20": [0], "21": [0], "22": [0], "23": [0], "24": [0]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/types/index.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/types/index.ts", "statementMap": {"0": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 29}}, "1": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 22}}, "2": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 26}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {}, "b": {}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/workflows/compliance-system.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/workflows/compliance-system.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 49}}, "2": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 59}}, "3": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 49}}, "4": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 55}}, "5": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 83}}, "6": {"start": {"line": 19, "column": 4}, "end": {"line": 19, "column": 56}}, "7": {"start": {"line": 22, "column": 4}, "end": {"line": 22, "column": 52}}, "8": {"start": {"line": 25, "column": 20}, "end": {"line": 25, "column": 71}}, "9": {"start": {"line": 26, "column": 4}, "end": {"line": 32, "column": 5}}, "10": {"start": {"line": 27, "column": 6}, "end": {"line": 27, "column": 68}}, "11": {"start": {"line": 29, "column": 6}, "end": {"line": 29, "column": 42}}, "12": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 54}}, "13": {"start": {"line": 31, "column": 6}, "end": {"line": 31, "column": 51}}, "14": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 66}}, "15": {"start": {"line": 37, "column": 23}, "end": {"line": 37, "column": 75}}, "16": {"start": {"line": 39, "column": 4}, "end": {"line": 39, "column": 63}}, "17": {"start": {"line": 42, "column": 4}, "end": {"line": 45, "column": 5}}, "18": {"start": {"line": 43, "column": 6}, "end": {"line": 43, "column": 64}}, "19": {"start": {"line": 44, "column": 6}, "end": {"line": 44, "column": 70}}, "20": {"start": {"line": 47, "column": 4}, "end": {"line": 47, "column": 58}}, "21": {"start": {"line": 49, "column": 19}, "end": {"line": 49, "column": 72}}, "22": {"start": {"line": 52, "column": 4}, "end": {"line": 52, "column": 62}}, "23": {"start": {"line": 53, "column": 4}, "end": {"line": 53, "column": 52}}, "24": {"start": {"line": 55, "column": 4}, "end": {"line": 55, "column": 95}}, "25": {"start": {"line": 57, "column": 4}, "end": {"line": 57, "column": 34}}, "26": {"start": {"line": 66, "column": 4}, "end": {"line": 66, "column": 83}}, "27": {"start": {"line": 68, "column": 4}, "end": {"line": 68, "column": 63}}, "28": {"start": {"line": 71, "column": 4}, "end": {"line": 71, "column": 31}}, "29": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 53}}, "30": {"start": {"line": 75, "column": 27}, "end": {"line": 75, "column": 70}}, "31": {"start": {"line": 77, "column": 4}, "end": {"line": 88, "column": 5}}, "32": {"start": {"line": 78, "column": 6}, "end": {"line": 78, "column": 59}}, "33": {"start": {"line": 80, "column": 6}, "end": {"line": 80, "column": 34}}, "34": {"start": {"line": 81, "column": 6}, "end": {"line": 81, "column": 30}}, "35": {"start": {"line": 82, "column": 6}, "end": {"line": 82, "column": 55}}, "36": {"start": {"line": 83, "column": 6}, "end": {"line": 87, "column": 8}}, "37": {"start": {"line": 90, "column": 4}, "end": {"line": 90, "column": 57}}, "38": {"start": {"line": 93, "column": 22}, "end": {"line": 93, "column": 72}}, "39": {"start": {"line": 94, "column": 4}, "end": {"line": 94, "column": 34}}, "40": {"start": {"line": 96, "column": 4}, "end": {"line": 96, "column": 57}}, "41": {"start": {"line": 99, "column": 28}, "end": {"line": 99, "column": 75}}, "42": {"start": {"line": 101, "column": 4}, "end": {"line": 112, "column": 5}}, "43": {"start": {"line": 102, "column": 6}, "end": {"line": 102, "column": 59}}, "44": {"start": {"line": 104, "column": 6}, "end": {"line": 104, "column": 34}}, "45": {"start": {"line": 105, "column": 6}, "end": {"line": 105, "column": 30}}, "46": {"start": {"line": 106, "column": 6}, "end": {"line": 106, "column": 55}}, "47": {"start": {"line": 107, "column": 6}, "end": {"line": 111, "column": 8}}, "48": {"start": {"line": 116, "column": 30}, "end": {"line": 116, "column": 32}}, "49": {"start": {"line": 118, "column": 4}, "end": {"line": 127, "column": 5}}, "50": {"start": {"line": 119, "column": 6}, "end": {"line": 119, "column": 30}}, "51": {"start": {"line": 120, "column": 6}, "end": {"line": 120, "column": 61}}, "52": {"start": {"line": 121, "column": 11}, "end": {"line": 127, "column": 5}}, "53": {"start": {"line": 122, "column": 6}, "end": {"line": 122, "column": 30}}, "54": {"start": {"line": 123, "column": 6}, "end": {"line": 123, "column": 73}}, "55": {"start": {"line": 125, "column": 6}, "end": {"line": 125, "column": 26}}, "56": {"start": {"line": 126, "column": 6}, "end": {"line": 126, "column": 64}}, "57": {"start": {"line": 130, "column": 4}, "end": {"line": 130, "column": 28}}, "58": {"start": {"line": 131, "column": 4}, "end": {"line": 131, "column": 37}}, "59": {"start": {"line": 132, "column": 4}, "end": {"line": 132, "column": 53}}, "60": {"start": {"line": 134, "column": 4}, "end": {"line": 134, "column": 51}}, "61": {"start": {"line": 135, "column": 4}, "end": {"line": 135, "column": 48}}, "62": {"start": {"line": 137, "column": 4}, "end": {"line": 137, "column": 95}}, "63": {"start": {"line": 139, "column": 4}, "end": {"line": 139, "column": 42}}, "64": {"start": {"line": 144, "column": 4}, "end": {"line": 144, "column": 67}}, "65": {"start": {"line": 146, "column": 4}, "end": {"line": 146, "column": 63}}, "66": {"start": {"line": 149, "column": 20}, "end": {"line": 149, "column": 70}}, "67": {"start": {"line": 151, "column": 4}, "end": {"line": 151, "column": 63}}, "68": {"start": {"line": 154, "column": 30}, "end": {"line": 154, "column": 77}}, "69": {"start": {"line": 156, "column": 4}, "end": {"line": 156, "column": 60}}, "70": {"start": {"line": 159, "column": 28}, "end": {"line": 159, "column": 93}}, "71": {"start": {"line": 161, "column": 4}, "end": {"line": 161, "column": 55}}, "72": {"start": {"line": 164, "column": 19}, "end": {"line": 167, "column": null}}, "73": {"start": {"line": 170, "column": 4}, "end": {"line": 170, "column": 54}}, "74": {"start": {"line": 171, "column": 4}, "end": {"line": 171, "column": 48}}, "75": {"start": {"line": 173, "column": 4}, "end": {"line": 173, "column": 78}}, "76": {"start": {"line": 175, "column": 4}, "end": {"line": 175, "column": 18}}, "77": {"start": {"line": 181, "column": 4}, "end": {"line": 181, "column": 82}}, "78": {"start": {"line": 184, "column": 20}, "end": {"line": 184, "column": 67}}, "79": {"start": {"line": 185, "column": 28}, "end": {"line": 185, "column": 83}}, "80": {"start": {"line": 188, "column": 4}, "end": {"line": 188, "column": 64}}, "81": {"start": {"line": 189, "column": 4}, "end": {"line": 189, "column": 67}}, "82": {"start": {"line": 191, "column": 4}, "end": {"line": 191, "column": 101}}, "83": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 13}}, "84": {"start": {"line": 12, "column": 15}, "end": {"line": 58, "column": null}}, "85": {"start": {"line": 61, "column": 15}, "end": {"line": 140, "column": null}}, "86": {"start": {"line": 143, "column": 15}, "end": {"line": 176, "column": null}}, "87": {"start": {"line": 180, "column": 15}, "end": {"line": 192, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": 8}}, "loc": {"start": {"line": 12, "column": 69}, "end": {"line": 58, "column": 3}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 61, "column": 2}, "end": {"line": 61, "column": 8}}, "loc": {"start": {"line": 61, "column": 53}, "end": {"line": 140, "column": 3}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 143, "column": 2}, "end": {"line": 143, "column": 8}}, "loc": {"start": {"line": 143, "column": 86}, "end": {"line": 176, "column": 3}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 180, "column": 2}, "end": {"line": 180, "column": 8}}, "loc": {"start": {"line": 180, "column": 79}, "end": {"line": 192, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 26, "column": 4}, "end": {"line": 32, "column": 5}}, "type": "if", "locations": [{"start": {"line": 26, "column": 4}, "end": {"line": 32, "column": 5}}]}, "1": {"loc": {"start": {"line": 42, "column": 4}, "end": {"line": 45, "column": 5}}, "type": "if", "locations": [{"start": {"line": 42, "column": 4}, "end": {"line": 45, "column": 5}}]}, "2": {"loc": {"start": {"line": 49, "column": 19}, "end": {"line": 49, "column": 72}}, "type": "cond-expr", "locations": [{"start": {"line": 49, "column": 43}, "end": {"line": 49, "column": 58}}, {"start": {"line": 49, "column": 61}, "end": {"line": 49, "column": 72}}]}, "3": {"loc": {"start": {"line": 77, "column": 4}, "end": {"line": 88, "column": 5}}, "type": "if", "locations": [{"start": {"line": 77, "column": 4}, "end": {"line": 88, "column": 5}}]}, "4": {"loc": {"start": {"line": 101, "column": 4}, "end": {"line": 112, "column": 5}}, "type": "if", "locations": [{"start": {"line": 101, "column": 4}, "end": {"line": 112, "column": 5}}]}, "5": {"loc": {"start": {"line": 118, "column": 4}, "end": {"line": 127, "column": 5}}, "type": "if", "locations": [{"start": {"line": 118, "column": 4}, "end": {"line": 127, "column": 5}}, {"start": {"line": 121, "column": 11}, "end": {"line": 127, "column": 5}}]}, "6": {"loc": {"start": {"line": 121, "column": 11}, "end": {"line": 127, "column": 5}}, "type": "if", "locations": [{"start": {"line": 121, "column": 11}, "end": {"line": 127, "column": 5}}, {"start": {"line": 124, "column": 11}, "end": {"line": 127, "column": 5}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 2, "6": 2, "7": 2, "8": 2, "9": 2, "10": 0, "11": 0, "12": 0, "13": 0, "14": 2, "15": 2, "16": 2, "17": 2, "18": 1, "19": 1, "20": 2, "21": 2, "22": 2, "23": 2, "24": 2, "25": 2, "26": 2, "27": 2, "28": 2, "29": 2, "30": 2, "31": 2, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 2, "38": 2, "39": 2, "40": 2, "41": 2, "42": 2, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 2, "49": 2, "50": 1, "51": 1, "52": 1, "53": 0, "54": 0, "55": 1, "56": 1, "57": 2, "58": 2, "59": 2, "60": 2, "61": 2, "62": 2, "63": 2, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 1, "84": 1, "85": 1, "86": 1, "87": 1}, "f": {"0": 2, "1": 2, "2": 0, "3": 0}, "b": {"0": [0], "1": [1], "2": [1, 1], "3": [0], "4": [0], "5": [1, 1], "6": [0, 1]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/workflows/document-processing.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/workflows/document-processing.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 49}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 59}}, "3": {"start": {"line": 13, "column": 4}, "end": {"line": 13, "column": 27}}, "4": {"start": {"line": 16, "column": 4}, "end": {"line": 19, "column": 5}}, "5": {"start": {"line": 17, "column": 6}, "end": {"line": 17, "column": 91}}, "6": {"start": {"line": 18, "column": 6}, "end": {"line": 18, "column": 19}}, "7": {"start": {"line": 21, "column": 4}, "end": {"line": 21, "column": 69}}, "8": {"start": {"line": 22, "column": 4}, "end": {"line": 22, "column": 16}}, "9": {"start": {"line": 27, "column": 4}, "end": {"line": 27, "column": 83}}, "10": {"start": {"line": 29, "column": 46}, "end": {"line": 29, "column": 48}}, "11": {"start": {"line": 32, "column": 4}, "end": {"line": 32, "column": 27}}, "12": {"start": {"line": 35, "column": 18}, "end": {"line": 35, "column": 69}}, "13": {"start": {"line": 37, "column": 4}, "end": {"line": 61, "column": 5}}, "14": {"start": {"line": 38, "column": 20}, "end": {"line": 38, "column": 50}}, "15": {"start": {"line": 39, "column": 22}, "end": {"line": 39, "column": 51}}, "16": {"start": {"line": 41, "column": 6}, "end": {"line": 60, "column": 7}}, "17": {"start": {"line": 43, "column": 28}, "end": {"line": 46, "column": null}}, "18": {"start": {"line": 49, "column": 8}, "end": {"line": 59, "column": 9}}, "19": {"start": {"line": 50, "column": 10}, "end": {"line": 58, "column": 13}}, "20": {"start": {"line": 63, "column": 4}, "end": {"line": 63, "column": 89}}, "21": {"start": {"line": 66, "column": 4}, "end": {"line": 68, "column": 5}}, "22": {"start": {"line": 67, "column": 6}, "end": {"line": 67, "column": 58}}, "23": {"start": {"line": 70, "column": 4}, "end": {"line": 70, "column": 22}}, "24": {"start": {"line": 80, "column": 4}, "end": {"line": 80, "column": 26}}, "25": {"start": {"line": 83, "column": 26}, "end": {"line": 83, "column": 29}}, "26": {"start": {"line": 84, "column": 23}, "end": {"line": 84, "column": 28}}, "27": {"start": {"line": 86, "column": 4}, "end": {"line": 107, "column": 5}}, "28": {"start": {"line": 87, "column": 25}, "end": {"line": 87, "column": 47}}, "29": {"start": {"line": 88, "column": 22}, "end": {"line": 90, "column": null}}, "30": {"start": {"line": 94, "column": 33}, "end": {"line": 97, "column": 8}}, "31": {"start": {"line": 99, "column": 28}, "end": {"line": 100, "column": null}}, "32": {"start": {"line": 100, "column": 8}, "end": {"line": 100, "column": 47}}, "33": {"start": {"line": 103, "column": 6}, "end": {"line": 106, "column": 7}}, "34": {"start": {"line": 104, "column": 8}, "end": {"line": 104, "column": 28}}, "35": {"start": {"line": 105, "column": 8}, "end": {"line": 105, "column": 14}}, "36": {"start": {"line": 109, "column": 4}, "end": {"line": 109, "column": 24}}, "37": {"start": {"line": 114, "column": 4}, "end": {"line": 114, "column": 85}}, "38": {"start": {"line": 117, "column": 4}, "end": {"line": 117, "column": 26}}, "39": {"start": {"line": 119, "column": 31}, "end": {"line": 119, "column": 80}}, "40": {"start": {"line": 119, "column": 54}, "end": {"line": 119, "column": 79}}, "41": {"start": {"line": 120, "column": 27}, "end": {"line": 120, "column": 72}}, "42": {"start": {"line": 120, "column": 50}, "end": {"line": 120, "column": 71}}, "43": {"start": {"line": 122, "column": 4}, "end": {"line": 125, "column": 5}}, "44": {"start": {"line": 123, "column": 6}, "end": {"line": 123, "column": 94}}, "45": {"start": {"line": 127, "column": 4}, "end": {"line": 130, "column": 5}}, "46": {"start": {"line": 128, "column": 6}, "end": {"line": 128, "column": 91}}, "47": {"start": {"line": 132, "column": 4}, "end": {"line": 132, "column": 59}}, "48": {"start": {"line": 137, "column": 4}, "end": {"line": 148, "column": 5}}, "49": {"start": {"line": 139, "column": 8}, "end": {"line": 139, "column": 73}}, "50": {"start": {"line": 141, "column": 8}, "end": {"line": 141, "column": 63}}, "51": {"start": {"line": 143, "column": 8}, "end": {"line": 143, "column": 52}}, "52": {"start": {"line": 145, "column": 8}, "end": {"line": 145, "column": 58}}, "53": {"start": {"line": 147, "column": 8}, "end": {"line": 147, "column": 54}}, "54": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 13}}, "55": {"start": {"line": 9, "column": 15}, "end": {"line": 23, "column": null}}, "56": {"start": {"line": 26, "column": 15}, "end": {"line": 71, "column": null}}, "57": {"start": {"line": 74, "column": 15}, "end": {"line": 110, "column": null}}, "58": {"start": {"line": 113, "column": 15}, "end": {"line": 133, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 9, "column": 2}, "end": {"line": 9, "column": 8}}, "loc": {"start": {"line": 9, "column": 60}, "end": {"line": 23, "column": 3}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 26, "column": 2}, "end": {"line": 26, "column": 8}}, "loc": {"start": {"line": 26, "column": 61}, "end": {"line": 71, "column": 3}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 74, "column": 2}, "end": {"line": 74, "column": 8}}, "loc": {"start": {"line": 77, "column": 21}, "end": {"line": 110, "column": 3}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 99, "column": 52}, "end": {"line": 99, "column": 59}}, "loc": {"start": {"line": 100, "column": 8}, "end": {"line": 100, "column": 47}}}, "4": {"name": "(anonymous_6)", "decl": {"start": {"line": 113, "column": 2}, "end": {"line": 113, "column": 8}}, "loc": {"start": {"line": 113, "column": 69}, "end": {"line": 133, "column": 3}}}, "5": {"name": "(anonymous_7)", "decl": {"start": {"line": 119, "column": 49}, "end": {"line": 119, "column": 50}}, "loc": {"start": {"line": 119, "column": 54}, "end": {"line": 119, "column": 79}}}, "6": {"name": "(anonymous_8)", "decl": {"start": {"line": 120, "column": 45}, "end": {"line": 120, "column": 46}}, "loc": {"start": {"line": 120, "column": 50}, "end": {"line": 120, "column": 71}}}, "7": {"name": "(anonymous_9)", "decl": {"start": {"line": 136, "column": 2}, "end": {"line": 136, "column": 8}}, "loc": {"start": {"line": 136, "column": 50}, "end": {"line": 149, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 16, "column": 4}, "end": {"line": 19, "column": 5}}, "type": "if", "locations": [{"start": {"line": 16, "column": 4}, "end": {"line": 19, "column": 5}}]}, "1": {"loc": {"start": {"line": 16, "column": 8}, "end": {"line": 16, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 16, "column": 8}, "end": {"line": 16, "column": 25}}, {"start": {"line": 16, "column": 29}, "end": {"line": 16, "column": 58}}]}, "2": {"loc": {"start": {"line": 41, "column": 6}, "end": {"line": 60, "column": 7}}, "type": "if", "locations": [{"start": {"line": 41, "column": 6}, "end": {"line": 60, "column": 7}}]}, "3": {"loc": {"start": {"line": 49, "column": 8}, "end": {"line": 59, "column": 9}}, "type": "if", "locations": [{"start": {"line": 49, "column": 8}, "end": {"line": 59, "column": 9}}]}, "4": {"loc": {"start": {"line": 66, "column": 4}, "end": {"line": 68, "column": 5}}, "type": "if", "locations": [{"start": {"line": 66, "column": 4}, "end": {"line": 68, "column": 5}}]}, "5": {"loc": {"start": {"line": 103, "column": 6}, "end": {"line": 106, "column": 7}}, "type": "if", "locations": [{"start": {"line": 103, "column": 6}, "end": {"line": 106, "column": 7}}]}, "6": {"loc": {"start": {"line": 103, "column": 10}, "end": {"line": 103, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 103, "column": 10}, "end": {"line": 103, "column": 24}}, {"start": {"line": 103, "column": 28}, "end": {"line": 103, "column": 56}}]}, "7": {"loc": {"start": {"line": 122, "column": 4}, "end": {"line": 125, "column": 5}}, "type": "if", "locations": [{"start": {"line": 122, "column": 4}, "end": {"line": 125, "column": 5}}]}, "8": {"loc": {"start": {"line": 127, "column": 4}, "end": {"line": 130, "column": 5}}, "type": "if", "locations": [{"start": {"line": 127, "column": 4}, "end": {"line": 130, "column": 5}}]}, "9": {"loc": {"start": {"line": 137, "column": 4}, "end": {"line": 148, "column": 5}}, "type": "switch", "locations": [{"start": {"line": 138, "column": 6}, "end": {"line": 139, "column": 73}}, {"start": {"line": 140, "column": 6}, "end": {"line": 141, "column": 63}}, {"start": {"line": 142, "column": 6}, "end": {"line": 143, "column": 52}}, {"start": {"line": 144, "column": 6}, "end": {"line": 145, "column": 58}}, {"start": {"line": 146, "column": 6}, "end": {"line": 147, "column": 54}}]}}, "s": {"0": 3, "1": 3, "2": 2, "3": 2, "4": 2, "5": 1, "6": 1, "7": 1, "8": 1, "9": 2, "10": 2, "11": 2, "12": 2, "13": 2, "14": 2, "15": 2, "16": 2, "17": 1, "18": 1, "19": 1, "20": 2, "21": 2, "22": 1, "23": 2, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 2, "38": 2, "39": 2, "40": 2, "41": 2, "42": 2, "43": 2, "44": 1, "45": 2, "46": 1, "47": 2, "48": 1, "49": 1, "50": 0, "51": 0, "52": 0, "53": 0, "54": 3, "55": 3, "56": 3, "57": 3, "58": 3}, "f": {"0": 2, "1": 2, "2": 0, "3": 0, "4": 2, "5": 2, "6": 2, "7": 1}, "b": {"0": [1], "1": [2, 2], "2": [1], "3": [1], "4": [1], "5": [0], "6": [0, 0], "7": [1], "8": [1], "9": [1, 0, 0, 0, 0]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/workflows/index.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/workflows/index.ts", "statementMap": {"0": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 36}}, "1": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 38}}, "2": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 33}}, "3": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 36}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/workflows/kyc-processing.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/workflows/kyc-processing.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 9, "column": 4}, "end": {"line": 9, "column": 78}}, "2": {"start": {"line": 12, "column": 27}, "end": {"line": 12, "column": 66}}, "3": {"start": {"line": 13, "column": 4}, "end": {"line": 13, "column": 37}}, "4": {"start": {"line": 16, "column": 24}, "end": {"line": 16, "column": 88}}, "5": {"start": {"line": 17, "column": 24}, "end": {"line": 17, "column": 115}}, "6": {"start": {"line": 18, "column": 28}, "end": {"line": 18, "column": 100}}, "7": {"start": {"line": 19, "column": 25}, "end": {"line": 19, "column": 90}}, "8": {"start": {"line": 21, "column": 23}, "end": {"line": 24, "column": 46}}, "9": {"start": {"line": 26, "column": 21}, "end": {"line": 26, "column": 38}}, "10": {"start": {"line": 28, "column": 4}, "end": {"line": 28, "column": 144}}, "11": {"start": {"line": 29, "column": 4}, "end": {"line": 29, "column": 36}}, "12": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 86}}, "13": {"start": {"line": 37, "column": 27}, "end": {"line": 37, "column": 66}}, "14": {"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": 37}}, "15": {"start": {"line": 40, "column": 20}, "end": {"line": 40, "column": 21}}, "16": {"start": {"line": 43, "column": 16}, "end": {"line": 43, "column": 99}}, "17": {"start": {"line": 44, "column": 4}, "end": {"line": 45, "column": 39}}, "18": {"start": {"line": 44, "column": 18}, "end": {"line": 44, "column": 34}}, "19": {"start": {"line": 45, "column": 9}, "end": {"line": 45, "column": 39}}, "20": {"start": {"line": 45, "column": 23}, "end": {"line": 45, "column": 39}}, "21": {"start": {"line": 48, "column": 32}, "end": {"line": 48, "column": 53}}, "22": {"start": {"line": 49, "column": 20}, "end": {"line": 49, "column": 68}}, "23": {"start": {"line": 50, "column": 4}, "end": {"line": 52, "column": 5}}, "24": {"start": {"line": 50, "column": 54}, "end": {"line": 50, "column": 80}}, "25": {"start": {"line": 51, "column": 6}, "end": {"line": 51, "column": 22}}, "26": {"start": {"line": 55, "column": 4}, "end": {"line": 55, "column": 48}}, "27": {"start": {"line": 57, "column": 4}, "end": {"line": 57, "column": 96}}, "28": {"start": {"line": 58, "column": 4}, "end": {"line": 58, "column": 36}}, "29": {"start": {"line": 63, "column": 4}, "end": {"line": 63, "column": 83}}, "30": {"start": {"line": 66, "column": 27}, "end": {"line": 66, "column": 66}}, "31": {"start": {"line": 67, "column": 4}, "end": {"line": 67, "column": 37}}, "32": {"start": {"line": 70, "column": 28}, "end": {"line": 70, "column": 54}}, "33": {"start": {"line": 71, "column": 21}, "end": {"line": 71, "column": 72}}, "34": {"start": {"line": 73, "column": 19}, "end": {"line": 76, "column": 6}}, "35": {"start": {"line": 78, "column": 4}, "end": {"line": 78, "column": 115}}, "36": {"start": {"line": 79, "column": 4}, "end": {"line": 79, "column": 18}}, "37": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 13}}, "38": {"start": {"line": 8, "column": 15}, "end": {"line": 30, "column": null}}, "39": {"start": {"line": 33, "column": 15}, "end": {"line": 59, "column": null}}, "40": {"start": {"line": 62, "column": 15}, "end": {"line": 80, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 8, "column": 2}, "end": {"line": 8, "column": 8}}, "loc": {"start": {"line": 8, "column": 49}, "end": {"line": 30, "column": 3}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 33, "column": 2}, "end": {"line": 33, "column": 8}}, "loc": {"start": {"line": 33, "column": 56}, "end": {"line": 59, "column": 3}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 50, "column": 44}, "end": {"line": 50, "column": 50}}, "loc": {"start": {"line": 50, "column": 54}, "end": {"line": 50, "column": 80}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 62, "column": 2}, "end": {"line": 62, "column": 8}}, "loc": {"start": {"line": 62, "column": 53}, "end": {"line": 80, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 16, "column": 24}, "end": {"line": 16, "column": 88}}, "type": "binary-expr", "locations": [{"start": {"line": 16, "column": 24}, "end": {"line": 16, "column": 48}}, {"start": {"line": 16, "column": 52}, "end": {"line": 16, "column": 88}}]}, "1": {"loc": {"start": {"line": 17, "column": 24}, "end": {"line": 17, "column": 115}}, "type": "binary-expr", "locations": [{"start": {"line": 17, "column": 24}, "end": {"line": 17, "column": 56}}, {"start": {"line": 17, "column": 60}, "end": {"line": 17, "column": 115}}]}, "2": {"loc": {"start": {"line": 18, "column": 28}, "end": {"line": 18, "column": 100}}, "type": "binary-expr", "locations": [{"start": {"line": 18, "column": 28}, "end": {"line": 18, "column": 56}}, {"start": {"line": 18, "column": 60}, "end": {"line": 18, "column": 100}}]}, "3": {"loc": {"start": {"line": 19, "column": 25}, "end": {"line": 19, "column": 90}}, "type": "binary-expr", "locations": [{"start": {"line": 19, "column": 25}, "end": {"line": 19, "column": 50}}, {"start": {"line": 19, "column": 54}, "end": {"line": 19, "column": 90}}]}, "4": {"loc": {"start": {"line": 21, "column": 24}, "end": {"line": 21, "column": 45}}, "type": "cond-expr", "locations": [{"start": {"line": 21, "column": 38}, "end": {"line": 21, "column": 41}}, {"start": {"line": 21, "column": 44}, "end": {"line": 21, "column": 45}}]}, "5": {"loc": {"start": {"line": 22, "column": 23}, "end": {"line": 22, "column": 45}}, "type": "cond-expr", "locations": [{"start": {"line": 22, "column": 37}, "end": {"line": 22, "column": 41}}, {"start": {"line": 22, "column": 44}, "end": {"line": 22, "column": 45}}]}, "6": {"loc": {"start": {"line": 23, "column": 23}, "end": {"line": 23, "column": 49}}, "type": "cond-expr", "locations": [{"start": {"line": 23, "column": 41}, "end": {"line": 23, "column": 45}}, {"start": {"line": 23, "column": 48}, "end": {"line": 23, "column": 49}}]}, "7": {"loc": {"start": {"line": 24, "column": 23}, "end": {"line": 24, "column": 45}}, "type": "cond-expr", "locations": [{"start": {"line": 24, "column": 38}, "end": {"line": 24, "column": 41}}, {"start": {"line": 24, "column": 44}, "end": {"line": 24, "column": 45}}]}, "8": {"loc": {"start": {"line": 28, "column": 57}, "end": {"line": 28, "column": 87}}, "type": "cond-expr", "locations": [{"start": {"line": 28, "column": 68}, "end": {"line": 28, "column": 76}}, {"start": {"line": 28, "column": 79}, "end": {"line": 28, "column": 87}}]}, "9": {"loc": {"start": {"line": 44, "column": 4}, "end": {"line": 45, "column": 39}}, "type": "if", "locations": [{"start": {"line": 44, "column": 4}, "end": {"line": 45, "column": 39}}, {"start": {"line": 45, "column": 9}, "end": {"line": 45, "column": 39}}]}, "10": {"loc": {"start": {"line": 45, "column": 9}, "end": {"line": 45, "column": 39}}, "type": "if", "locations": [{"start": {"line": 45, "column": 9}, "end": {"line": 45, "column": 39}}]}, "11": {"loc": {"start": {"line": 50, "column": 4}, "end": {"line": 52, "column": 5}}, "type": "if", "locations": [{"start": {"line": 50, "column": 4}, "end": {"line": 52, "column": 5}}]}, "12": {"loc": {"start": {"line": 50, "column": 8}, "end": {"line": 50, "column": 81}}, "type": "binary-expr", "locations": [{"start": {"line": 50, "column": 8}, "end": {"line": 50, "column": 15}}, {"start": {"line": 50, "column": 19}, "end": {"line": 50, "column": 81}}]}, "13": {"loc": {"start": {"line": 75, "column": 15}, "end": {"line": 75, "column": 74}}, "type": "cond-expr", "locations": [{"start": {"line": 75, "column": 26}, "end": {"line": 75, "column": 62}}, {"start": {"line": 75, "column": 65}, "end": {"line": 75, "column": 74}}]}, "14": {"loc": {"start": {"line": 78, "column": 51}, "end": {"line": 78, "column": 85}}, "type": "cond-expr", "locations": [{"start": {"line": 78, "column": 62}, "end": {"line": 78, "column": 75}}, {"start": {"line": 78, "column": 78}, "end": {"line": 78, "column": 85}}]}}, "s": {"0": 2, "1": 2, "2": 2, "3": 2, "4": 2, "5": 2, "6": 2, "7": 2, "8": 2, "9": 2, "10": 2, "11": 2, "12": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "18": 0, "19": 1, "20": 0, "21": 1, "22": 1, "23": 1, "24": 3, "25": 0, "26": 1, "27": 1, "28": 1, "29": 2, "30": 2, "31": 2, "32": 2, "33": 2, "34": 2, "35": 2, "36": 2, "37": 2, "38": 2, "39": 2, "40": 2}, "f": {"0": 2, "1": 1, "2": 3, "3": 2}, "b": {"0": [2, 2], "1": [2, 2], "2": [2, 2], "3": [2, 2], "4": [1, 1], "5": [1, 1], "6": [1, 1], "7": [2, 0], "8": [1, 1], "9": [0, 1], "10": [0], "11": [0], "12": [1, 1], "13": [1, 1], "14": [1, 1]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/workflows/report-generation.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/workflows/report-generation.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 49}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 59}}, "3": {"start": {"line": 13, "column": 4}, "end": {"line": 13, "column": 27}}, "4": {"start": {"line": 16, "column": 20}, "end": {"line": 16, "column": 67}}, "5": {"start": {"line": 18, "column": 4}, "end": {"line": 18, "column": 83}}, "6": {"start": {"line": 19, "column": 4}, "end": {"line": 19, "column": 19}}, "7": {"start": {"line": 24, "column": 4}, "end": {"line": 24, "column": 52}}, "8": {"start": {"line": 26, "column": 4}, "end": {"line": 26, "column": 27}}, "9": {"start": {"line": 28, "column": 38}, "end": {"line": 28, "column": 40}}, "10": {"start": {"line": 30, "column": 4}, "end": {"line": 45, "column": 5}}, "11": {"start": {"line": 31, "column": 6}, "end": {"line": 44, "column": 7}}, "12": {"start": {"line": 32, "column": 8}, "end": {"line": 43, "column": 9}}, "13": {"start": {"line": 34, "column": 12}, "end": {"line": 34, "column": 91}}, "14": {"start": {"line": 35, "column": 12}, "end": {"line": 35, "column": 96}}, "15": {"start": {"line": 36, "column": 12}, "end": {"line": 36, "column": 18}}, "16": {"start": {"line": 38, "column": 12}, "end": {"line": 38, "column": 84}}, "17": {"start": {"line": 39, "column": 12}, "end": {"line": 39, "column": 18}}, "18": {"start": {"line": 41, "column": 12}, "end": {"line": 41, "column": 85}}, "19": {"start": {"line": 42, "column": 12}, "end": {"line": 42, "column": 18}}, "20": {"start": {"line": 47, "column": 4}, "end": {"line": 47, "column": 76}}, "21": {"start": {"line": 48, "column": 4}, "end": {"line": 48, "column": 27}}, "22": {"start": {"line": 59, "column": 4}, "end": {"line": 59, "column": 54}}, "23": {"start": {"line": 61, "column": 4}, "end": {"line": 61, "column": 27}}, "24": {"start": {"line": 64, "column": 20}, "end": {"line": 64, "column": 67}}, "25": {"start": {"line": 66, "column": 4}, "end": {"line": 66, "column": 129}}, "26": {"start": {"line": 68, "column": 4}, "end": {"line": 68, "column": 19}}, "27": {"start": {"line": 82, "column": 4}, "end": {"line": 82, "column": 53}}, "28": {"start": {"line": 84, "column": 4}, "end": {"line": 84, "column": 26}}, "29": {"start": {"line": 86, "column": 37}, "end": {"line": 93, "column": 6}}, "30": {"start": {"line": 96, "column": 4}, "end": {"line": 96, "column": 58}}, "31": {"start": {"line": 98, "column": 4}, "end": {"line": 98, "column": 87}}, "32": {"start": {"line": 99, "column": 4}, "end": {"line": 99, "column": 18}}, "33": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 13}}, "34": {"start": {"line": 9, "column": 15}, "end": {"line": 20, "column": null}}, "35": {"start": {"line": 23, "column": 15}, "end": {"line": 49, "column": null}}, "36": {"start": {"line": 53, "column": 15}, "end": {"line": 69, "column": null}}, "37": {"start": {"line": 72, "column": 15}, "end": {"line": 100, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 9, "column": 2}, "end": {"line": 9, "column": 8}}, "loc": {"start": {"line": 9, "column": 37}, "end": {"line": 20, "column": 3}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": 8}}, "loc": {"start": {"line": 23, "column": 66}, "end": {"line": 49, "column": 3}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 53, "column": 2}, "end": {"line": 53, "column": 8}}, "loc": {"start": {"line": 53, "column": 40}, "end": {"line": 69, "column": 3}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 72, "column": 2}, "end": {"line": 72, "column": 8}}, "loc": {"start": {"line": 80, "column": 29}, "end": {"line": 100, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 31, "column": 6}, "end": {"line": 44, "column": 7}}, "type": "if", "locations": [{"start": {"line": 31, "column": 6}, "end": {"line": 44, "column": 7}}]}, "1": {"loc": {"start": {"line": 32, "column": 8}, "end": {"line": 43, "column": 9}}, "type": "switch", "locations": [{"start": {"line": 33, "column": 10}, "end": {"line": 36, "column": 18}}, {"start": {"line": 37, "column": 10}, "end": {"line": 39, "column": 18}}, {"start": {"line": 40, "column": 10}, "end": {"line": 42, "column": 18}}]}}, "s": {"0": 2, "1": 2, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 3, "12": 3, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "18": 1, "19": 1, "20": 1, "21": 1, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 2, "34": 2, "35": 2, "36": 2, "37": 2}, "f": {"0": 1, "1": 1, "2": 0, "3": 0}, "b": {"0": [3], "1": [1, 1, 1]}}}