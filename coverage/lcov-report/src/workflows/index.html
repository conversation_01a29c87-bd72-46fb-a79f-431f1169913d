
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/workflows</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> src/workflows</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">69.56% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>160/230</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">74.13% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>43/58</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">70% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>14/20</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">69.77% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>157/225</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="compliance-system.ts"><a href="compliance-system.ts.html">compliance-system.ts</a></td>
	<td data-value="60.22" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 60%"></div><div class="cover-empty" style="width: 40%"></div></div>
	</td>
	<td data-value="60.22" class="pct medium">60.22%</td>
	<td data-value="88" class="abs medium">53/88</td>
	<td data-value="60" class="pct medium">60%</td>
	<td data-value="10" class="abs medium">6/10</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="4" class="abs medium">2/4</td>
	<td data-value="60.22" class="pct medium">60.22%</td>
	<td data-value="88" class="abs medium">53/88</td>
	</tr>

<tr>
	<td class="file medium" data-value="document-processing.ts"><a href="document-processing.ts.html">document-processing.ts</a></td>
	<td data-value="71.18" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 71%"></div><div class="cover-empty" style="width: 29%"></div></div>
	</td>
	<td data-value="71.18" class="pct medium">71.18%</td>
	<td data-value="59" class="abs medium">42/59</td>
	<td data-value="56.25" class="pct medium">56.25%</td>
	<td data-value="16" class="abs medium">9/16</td>
	<td data-value="75" class="pct medium">75%</td>
	<td data-value="8" class="abs medium">6/8</td>
	<td data-value="70.17" class="pct medium">70.17%</td>
	<td data-value="57" class="abs medium">40/57</td>
	</tr>

<tr>
	<td class="file low" data-value="index.ts"><a href="index.ts.html">index.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="4" class="abs low">0/4</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="4" class="abs low">0/4</td>
	</tr>

<tr>
	<td class="file high" data-value="kyc-processing.ts"><a href="kyc-processing.ts.html">kyc-processing.ts</a></td>
	<td data-value="92.68" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 92%"></div><div class="cover-empty" style="width: 8%"></div></div>
	</td>
	<td data-value="92.68" class="pct high">92.68%</td>
	<td data-value="41" class="abs high">38/41</td>
	<td data-value="85.71" class="pct high">85.71%</td>
	<td data-value="28" class="abs high">24/28</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="97.36" class="pct high">97.36%</td>
	<td data-value="38" class="abs high">37/38</td>
	</tr>

<tr>
	<td class="file medium" data-value="report-generation.ts"><a href="report-generation.ts.html">report-generation.ts</a></td>
	<td data-value="71.05" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 71%"></div><div class="cover-empty" style="width: 29%"></div></div>
	</td>
	<td data-value="71.05" class="pct medium">71.05%</td>
	<td data-value="38" class="abs medium">27/38</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="4" class="abs medium">2/4</td>
	<td data-value="71.05" class="pct medium">71.05%</td>
	<td data-value="38" class="abs medium">27/38</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-13T19:54:13.617Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    